PROJECT_NAME := $(shell git remote -v | head -n1 | awk '{print $$2}' | sed 's/.*\///' | sed 's/\.git//')

.PHONY: dockerized-tests
dockerized-tests:
	echo "Running tests with docker"
	docker-compose -f docker-compose.yml build --force-rm
	docker-compose -f docker-compose.yml run pytest
	docker-compose -f docker-compose.yml down --remove-orphans

.PHONY: install
install:
	pip install pre-commit && pre-commit install
	poetry install --no-dev

.PHONY: dev-install
dev-install:
	pip install pre-commit && pre-commit install
	poetry install

.PHONY: bump-major
bump-major:
	poetry version major

.PHONY: bump-minor
bump-minor:
	poetry version minor

.PHONY: bump-patch
bump-patch:
	poetry version patch

.PHONY: update-changelog
update-changelog:
	docker run -e CHANGELOG_GITHUB_TOKEN=${VAULT_TOKEN} -it --rm -v `pwd`:/usr/local/src/your-app githubchangeloggenerator/github-changelog-generator --user grofers --project ${PROJECT_NAME}

.PHONY: docker-release
docker-release:
	docker-compose -f docker-compose.release.yml build --force-rm --no-cache
	docker-compose -f docker-compose.release.yml run release_production
	docker-compose -f docker-compose.release.yml down --remove-orphans

.PHONY: docker-release-test
docker-release-test:
	docker-compose -f docker-compose.release.yml build --force-rm
	docker-compose -f docker-compose.release.yml run release_test
	docker-compose -f docker-compose.release.yml down --remove-orphans
