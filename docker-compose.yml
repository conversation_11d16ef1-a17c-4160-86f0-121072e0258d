version: '3'

services:
  postgres:
    image: postgres:13.5
    environment:
    - POSTGRES_PASSWORD=test
    - POSTGRES_USER=test
    - POSTGRES_DB=test
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
    - pencilbox_network

  mysql:
    image: mysql:8.0.21
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: test
      MYSQL_DATABASE: test
      MYSQL_USER: test
      MYSQL_PASSWORD: test
    healthcheck:
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost"]
      timeout: 5s
      retries: 10
    networks:
    - pencilbox_network

  pytest:
    container_name: test_pencilbox
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        ENVIORNMENT: test
    environment:
    - VAULT_URL=${VAULT_URL}
    - VAULT_TOKEN=${VAULT_TOKEN}
    - VAULT_SERVICE_TOKEN=${VAULT_SERVICE_TOKEN}
    - mysql_uri=mysql://test:test@mysql/test
    - postgres_uri=************************************
    command: >
      bash -c 'while !</dev/tcp/mysql/3306; do sleep 1; done
      && while !</dev/tcp/postgres/5432; do sleep 1; done
      && pytest --verbose --no-cov'
    restart: on-failure
    depends_on:
    - "postgres"
    - "mysql"
    networks:
      pencilbox_network:
        ipv4_address: *************
        ipv6_address: 2001:3984:3990::10

networks:
  pencilbox_network:
    ipam:
      driver: default
      config:
        - subnet: "************/24"
        - subnet: "2001:3984:3990::/64"
