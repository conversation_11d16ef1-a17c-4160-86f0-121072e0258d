```
                        _ _ _
  _ __   ___ _ __   ___(_) | |__   _____  __
 | '_ \ / _ \ '_ \ / __| | | '_ \ / _ \ \/ /
 | |_) |  __/ | | | (__| | | |_) | (_) >  <
 | .__/ \___|_| |_|\___|_|_|_.__/ \___/_/\_\
 |_|

```

# Pencilbox

This is a utility library that provides reusable methods for your jupyter
notebooks.

## Installation

Pencilbox requires AWS (`aws configure`) and Vault credentials (`VAULT_URL` and `VAULT_TOKEN` env vars) to be configured on your machine
Connect to VPN.

After that, you can clone this repo and install Pencilbox using pip:

<pre>
$ <NAME_EMAIL>:grofers/pencilbox
$ cd pencilbox
$ make install
</pre>

or if you wish to set up a development environment,

<pre>
$ < clone the repository, cd into pencilbox >
$ make dev-install
</pre>

## Usage

You can easily do the following tasks using pencilbox:

### Get a database connection to run a SQL query

<pre>
import pandas as pd
import pencilbox as pb

sql = "SELECT * FROM metrics.complaints LIMIT 10"
con = pb.get_connection("[Warehouse] Redshift")

df = pd.read_sql_query(sql=sql, con=con)
</pre>

#### Available Database Connections

- `[Warehouse] Redshift` - Data warehouse connection
- `[Warehouse] Trino` - Trino query engine connection
- `[Database] Forecasting Dashboard` - Forecasting dashboard RDS PostgreSQL connection

<pre>
# Example: Connect to forecasting dashboard
import pandas as pd
import pencilbox as pb

sql = "SELECT * FROM your_forecasting_table LIMIT 10"
con = pb.get_connection("[Database] Forecasting Dashboard")

df = pd.read_sql_query(sql=sql, con=con)
</pre>

### Upload files to S3

<pre>
import pencilbox as pb

local_filename = "data.csv"
bucket_name = "grofers-test-dse"
cloud_filepath = "pencilbox/chaitra/availability/data.csv"
pb.to_s3(local_filename, bucket_name, cloud_filepath)
</pre>

### Download files from S3

<pre>
import pencilbox as pb

local_filename = "data.csv"
bucket_name = "grofers-test-dse"
cloud_filepath = "pencilbox/chaitra/availability/data.csv"
pb.from_s3(bucket_name, cloud_filepath, local_filename)
</pre>

### Load data from a CSV or pandas dataframe to Trino

<pre>
import pandas as pd
import pencilbox as pb
from datetime import datetime

df = pd.DataFrame(data={'id': [1],'date': [datetime.now()]})

kwargs = {
    "schema_name": "playground",
    "table_name": "test_to_trino",
    "column_dtypes": [
        {"name": "id", "type": "integer", "description": "About id column"},
        {"name": "create_date", "type": "timestamp", "description": "About date column"}
    ],
    "primary_key": ["id"],
    "partition_key": ["create_date"],
    "incremental_key": "create_date",
    "load_type": "rebuild", # append, rebuild, truncate or upsert,
    "table_description": "Something about the table",
}

pb.to_trino(df, **kwargs)
</pre>

**schema_name**: schema of the table being written

**table_name**: name of the table

**column_dtypes**: list of column names in the table with their trino data types and column description

**primary_key**: column_names which are unique in the table. trino does not have concept of primary keys, it is only required for `upsert` load_type to identify the rows that needs to be updated

**partition_key**: column_names by which you want to partition the table for better performance while reading data

**incremental_key**: column_name which should be used incase you want to upsert data on some column other than the primary key. row with larger incremental_key value will always replace row with smaller incremental_key value

**load_type**:

- upsert: Update existing data and insert any new data in the final table
  - Upsert without incremental key (default):
    - data will always get upserted on primary_key
    - data will be updated on following condition: `old.primary_key = new.primary_key`
  - Upsert with incremental key:
    - data will be upserted on primary_key as well as the incremental_key
    - data will be updated on following condition: `old.primary_key = new.primary_key and old.incremental_key <= new.incremental_key`
    - 🔴 Note: upsert with incremental_key can lead to duplicate rows if `new.incremental_key < old.incremental_key`. To avoid you will need to pass `"force_upsert_without_increment_check": False` in kwargs
- append: Insert all data in the final table as it is without checking existing table
- rebuild: Drop the existing table and create new with the given dataframe
- truncate: Delete the existing data from the table and push the dataframe to the table

### Load data from a pandas dataframe to Redshift

<pre>
import pandas as pd
import pencilbox as pb
from datetime import datetime

local_filename = "data.csv"
# or
df = pd.DataFrame(data={'id': [1],'date': [datetime.now()]})

kwargs = {
    "schema_name": "playground",
    "table_name": "test_to_redshift",
    "column_dtypes": [
        {"name": "id", "type": "integer", "description": "About id column"},
        {"name": "date", "type": "timestamp", "description": "About date column"}
    ],
    "primary_key": ["id"],
    "sortkey": ["date"],
    "incremental_key": "date",
    "load_type": "rebuild", # append, rebuild, truncate or upsert,
    "table_description": "Something about the table",
}

pb.to_redshift(df, **kwargs)
# or
pb.to_redshift(local_filename, **kwargs)
</pre>

### Load Metadata to data-catalog ( datahub )

<pre>
import pencilbox as pb

pb.to_datahub(
    schema_name="playground",
    table_name="test_to_datahub",
    column_dtypes=[
        {"name": "id", "type": "integer", "description": "About id column"},
        {"name": "date", "type": "timestamp", "description": "About date column"}
    ],
    table_description="Something about the table",
    data_platform="redshift", # Platform for which you are uploading metadata e.g. : Aurora, Pricing, etc
)
</pre>

### Save dataframe to temporary file

<pre>
import pencilbox as pb

filepath = pb.to_csv(df, 'data.csv')
</pre>

### Send mail

<pre>
import pencilbox as pb

from_email = "<EMAIL>"
to_email = ["<EMAIL>"]
subject = "What's up?"
html_content = "&lt;p&gt;Where art thou? Long time no see!&lt;p&gt;"

pb.send_email(from_email, to_email, subject, html_content, files=['data.csv'])
</pre>

### Send Slack message

<pre>
import pencilbox as pb

pb.send_slack_message(channel="dse-slackbot-tests", text="Change Da World… My Final Message")
</pre>

To tag a user in your slack message

<pre>
import pencilbox as pb

# UTK7UQE7R is the slack user id
pb.send_slack_message(channel="dse-slackbot-tests", text="hello <@UTK7UQE7R>")
</pre>

Formatting Messages: https://api.slack.com/reference/surfaces/formatting

### Interacting with google sheets

Before you start interacting with google sheets make sure that you have relevant access

We need both the sheetid and sheetname to access a google sheet. The sheetid can be extracted from the url
(Generic google sheet URL: https://docs.google.com/spreadsheets/d/sheetid/edit#gid=0) whereas the sheetname is the name given to that specific sheet. The default name is Sheet1

**Fetching a google sheet as a dataframe**

<pre>
import pencilbox as pb

sheet_id = '1BKRmhxSRdnQ_hm0H0kbDaIKVeaIHafNjpiF82dWGI48'
sheet_name = 'test'
df = pb.from_sheets(sheet_id, sheet_name)
</pre>

**Uploading a dataframe to google sheets**

<pre>
pb.to_sheets(df, sheet_id, sheet_name)
</pre>

**Fetching a gspread [worksheet](https://docs.gspread.org/en/latest/user-guide.html#selecting-a-worksheet)**

<pre>
pb.get_gsheet(df, sheet_id, sheet_name)
</pre>

**FAQ**

If you are facing any kind of issues with the API, mostly unauthorized or request denied errors, it is because of the fact that the permissions were not updated on the client side since pencilbox caches them or your client has timed out. So reloading your notebook to fetch the client again will solve the issue or if the permission changes are frequent you can set the parameter `clear_cache=True` to clear all caches while interacting with google sheets.

## Testing

<pre>
$ make test
</pre>

## Releasing

Pencilbox follows [Semantic Versioning](https://semver.org/).
After Pencilbox is ready to be released, these steps need to be followed.

1. Bump version. For instance, after a critical bugfix,

   $ make bump-patch

2. Push tagged commits to GitHub. Once the tag has been identified by Jenkins, the job has to be triggered.

   $ git push origin <branch-name> --follow-tags

3. Update changelog. This will pull GitHub activities like issues, PRs, and generate changelog in Markdown format. Push commit to GitHub.

   $ make update-changelog

4. Create a release on [https://github.com/grofers/pencilbox/releases/](https://github.com/grofers/pencilbox/releases/) - Optional.
