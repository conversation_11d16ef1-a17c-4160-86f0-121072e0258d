"""Test slack client."""

import pytest
import pencilbox
from pencilbox.connections import get_slack_client
from pencilbox.constants import SLACK_BOT_NAME, TEST_SLACK_CHANNEL


def test_slack_client():
    client = get_slack_client(SLACK_BOT_NAME)
    auth_test = client.auth_test()
    assert auth_test._initial_data["ok"] == True
    assert auth_test._initial_data["user"] == "banalyticsbot"

def test_slack_message():
    message = "This is a Test"
    response = pencilbox.send_slack_message(TEST_SLACK_CHANNEL, text=message)

    actual_message_text = response[0].data["message"]["text"].split("\n")[0]

    assert actual_message_text == message 
