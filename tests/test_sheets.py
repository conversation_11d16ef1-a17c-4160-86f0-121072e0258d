# -*- coding: utf-8 -*-

import pencilbox


# <EMAIL>

# TODO : Fix these tests for delegation support
# def test_sheet_old(
#     sheetid="15S5cQcvj3BOVuOykzgaEgjF35r68ygWG6GjB8p8qhG0", sheetname="test"
# ):
#     """
#     Testing google sheet download with old account
#     """

#     df = pencilbox.from_sheets(sheetid, sheetname)
#     values_real = [["1", "2"], ["3", "4"], ["5", "6"], ["7", "8"]]
#     values_fromsheet = df.values.tolist()
#     assert values_real == values_fromsheet  # test if the dataframe values are the same


# def test_sheet_new(
#     sheetid="15S5cQcvj3BOVuOykzgaEgjF35r68ygWG6GjB8p8qhG0", sheetname="test"
# ):
#     """
#     Testing google sheet download with new service account
#     """
#     df = pencilbox.from_sheets(sheetid, sheetname, service_account="service_account")
#     values_real = [["1", "2"], ["3", "4"], ["5", "6"], ["7", "8"]]
#     values_fromsheet = df.values.tolist()
#     assert values_real == values_fromsheet  # test if the dataframe values are the same
