# -*- coding: utf-8 -*-

from pencilbox.connections import (
    get_hvac_client,
    _client,
    _conn_cache,
    get_connection,
    get_secret,
    get_conn_dict,
)
from pencilbox.constants import PROD_CONNECTION_MAP_VAULT_PATH


def test_client_cache():
    get_hvac_client()
    assert _client["hvac"] == get_hvac_client()


def test_conn_cache():
    get_connection("[Warehouse] Trino")
    assert get_connection("[Warehouse] Trino") == _conn_cache["engine"]["[Warehouse] Trino"]


def test_secret_cache():
    get_secret(PROD_CONNECTION_MAP_VAULT_PATH)
    assert _conn_cache["secret"][PROD_CONNECTION_MAP_VAULT_PATH] == get_secret(
        PROD_CONNECTION_MAP_VAULT_PATH
    )
