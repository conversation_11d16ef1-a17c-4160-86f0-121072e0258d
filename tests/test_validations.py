# -*- coding: utf-8 -*-
import pytest

from pencilbox.redshift import (
    ToRedshiftValidator,
)


@pytest.fixture
def catalog_required_schemas():
    return ["abcd"]


@pytest.fixture
def allowed_schemas_write():
    return ["abcd"]


@pytest.fixture
def fixture_kwargs():
    kwargs = {
        "table_description": "testing table description",
        "table_name": "testing",
        "column_dtypes": [
            {"name": "order_id", "type": "int8", "description": "testing column description"},
            {
                "name": "cart_checkout_ts_ist",
                "type": "timestamp",
                "description": "testing column description",
            },
            {
                "name": "install_ts",
                "type": "timestamp",
                "description": "testing column description",
            },
            {"name": "cart_id", "type": "REAL", "description": "testing column description"},
        ],
    }
    return kwargs


@pytest.mark.parametrize(
    "invalid_name", [("abcd"), ("1abcd"), ("abcd.efgh"), ("abc@defgh"), ("_abcdef")]
)
def test_validate_table_for_invalid_name(
    invalid_name, catalog_required_schemas, allowed_schemas_write
):
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    with pytest.raises(Exception):
        to_redshift_validator.validate_table(invalid_name)


@pytest.mark.parametrize("valid_name", [("abcde"), ("abcd_fg"), ("abcd12efgh"), ("abc1_2_3_defgh")])
def test_validate_table_for_valid_name(valid_name, catalog_required_schemas, allowed_schemas_write):
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    to_redshift_validator.validate_table(valid_name)


def test_validate_table_description_for_jhub(
    fixture_kwargs, catalog_required_schemas, allowed_schemas_write
):
    fixture_kwargs["table_description"] = ""
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    with pytest.raises(Exception):
        to_redshift_validator.validate_table_description(**fixture_kwargs)


def test_validate_column_description_for_jhub(
    fixture_kwargs, catalog_required_schemas, allowed_schemas_write
):
    fixture_kwargs["column_dtypes"].append({"name": "merchant_id", "type": "int8"})
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    with pytest.raises(Exception):
        to_redshift_validator.validate_column_description(**fixture_kwargs)


@pytest.mark.parametrize("allowed_schemas", ["abcde", "fghij", "klmno"])
def test_validate_allowed_schemas_write(allowed_schemas_write, allowed_schemas):
    allowed_schemas_write.extend(["abcde", "fghij", "klmno"])
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    to_redshift_validator.validate_allowed_schema_write(allowed_schemas)


@pytest.mark.parametrize("restricted_schemas", ["hello", "world"])
def test_validate_restricted_schemas_write(allowed_schemas_write, restricted_schemas):
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    with pytest.raises(Exception):
        to_redshift_validator.validate_allowed_schema_write(restricted_schemas)


@pytest.mark.parametrize("catalog_schemas", ["abcde", "fghij", "klmno"])
def test_validate_catalog_required_schemas(
    fixture_kwargs, catalog_required_schemas, catalog_schemas
):
    catalog_required_schemas.extend(["abcde", "fghij", "klmno"])
    to_redshift_validator = ToRedshiftValidator(
        user_source="Pencilbox|JHub",
        user_email="<EMAIL>",
        allowed_schemas=allowed_schemas_write,
        catalog_schemas=catalog_required_schemas,
    )
    to_redshift_validator.validate_catalog_required_schema(catalog_schemas, **fixture_kwargs)
