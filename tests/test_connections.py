# -*- coding: utf-8 -*-

import os
from unittest.mock import patch

import pandas
import pencilbox as pb
from pencilbox.connections import _clear_cache
from pencilbox.constants import PROD_CONNECTION_MAP_VAULT_PATH, JHUB_CONNECTION_MAP_VAULT_PATH, PENCILBOX_CONFIG_VAULT_PATH


def get_secret(path):
    mysql_uri = os.environ.get("mysql_uri", "mysql://test:test@127.0.0.1:3306/test")
    postgres_uri = os.environ.get("postgres_uri", "postgresql://test:test@localhost:5432/test")
    forecasting_dashboard_uri = os.environ.get("forecasting_dashboard_uri", "postgresql://test:test@localhost:5432/forecasting_dashboard")

    _secret = {
        PROD_CONNECTION_MAP_VAULT_PATH: {
            "mysql": "mysql",
            "postgres": "postgres",
            "[Database] Forecasting Dashboard": "forecasting_dashboard"
        },
        JHUB_CONNECTION_MAP_VAULT_PATH: {
            "mysql": "mysql",
            "postgres": "postgres",
            "[Database] Forecasting Dashboard": "forecasting_dashboard"
        },
        "mysql": {"type": "mysql", "uri": mysql_uri},
        "postgres": {"type": "postgres", "uri": postgres_uri},
        "forecasting_dashboard": {"type": "postgres", "uri": forecasting_dashboard_uri},
        PENCILBOX_CONFIG_VAULT_PATH: {
            "allowed_to_redshift_users": "<EMAIL>",
            "allowed_get_connection_users": {"[Warehouse] Redshift": "<EMAIL>"},
        },
    }

    return _secret[path]


def query_annotation_check(conn_type, env_type):
    def get_query(conn_type, result):
        if conn_type == "postgres":
            q = result.cursor.query.decode("utf-8")
        elif conn_type == "mysql":
            q = result.cursor._executed.decode("utf-8")
        else:
            raise ValueError("Unknown connection type")
        return q

    con = pb.get_connection(conn_type)

    # Test_1: Simple query
    result = con.execute("select 1")
    q = get_query(conn_type, result)
    if env_type == "jhub":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:95adb6e77a0884d9e50232cb8c5c969d TaskID:adhoc Source:Pencilbox|JHub */"
        )
    elif env_type == "airflow":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:95adb6e77a0884d9e50232cb8c5c969d TaskID:dag_1234 Source:Pencilbox|Airflow */"
        )
    elif env_type == "unknown":
        assert (
            q.split("\n")[0]
            == f"/* User:test QueryHash:95adb6e77a0884d9e50232cb8c5c969d TaskID:adhoc Source:Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')} */"
        )

    assert result.fetchall() == [(1,)]

    # Test_2: Simple query with existing comments
    result = con.execute(
        """
    -- single line in query
    /* multiline comment in query */
    select 1
    """
    )
    q = get_query(conn_type, result)

    if env_type == "jhub":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:a9b57e678ed0ee180e4448d054ee8ee5 TaskID:adhoc Source:Pencilbox|JHub */"
        )
    elif env_type == "airflow":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:a9b57e678ed0ee180e4448d054ee8ee5 TaskID:dag_1234 Source:Pencilbox|Airflow */"
        )
    elif env_type == "unknown":
        assert (
            q.split("\n")[0]
            == f"/* User:test QueryHash:a9b57e678ed0ee180e4448d054ee8ee5 TaskID:adhoc Source:Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')} */"
        )

    assert result.fetchall() == [(1,)]

    # Test_3: Query with parameters interpreted by **kwargs
    result = con.execute(
        """
    -- single line in query
    /* multiline comment in query */
    select 1 where 1 = %(one)s
    """,
        one=1,
    )
    q = get_query(conn_type, result)

    if env_type == "jhub":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:ae5631fb7d2d48609afa1589494d4e08 TaskID:adhoc Source:Pencilbox|JHub */"
        )
    elif env_type == "airflow":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:ae5631fb7d2d48609afa1589494d4e08 TaskID:dag_1234 Source:Pencilbox|Airflow */"
        )
    elif env_type == "unknown":
        assert (
            q.split("\n")[0]
            == f"/* User:test QueryHash:ae5631fb7d2d48609afa1589494d4e08 TaskID:adhoc Source:Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')} */"
        )

    assert result.fetchall() == [(1,)]

    # Test_4: Query with parameters interpreted by *args
    result = con.execute(
        """
    -- single line in query
    /* multiline comment in query */
    select 1 where 1 = %(one)s
    """,
        {"one": 1},
    )
    q = get_query(conn_type, result)

    if env_type == "jhub":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:ae5631fb7d2d48609afa1589494d4e08 TaskID:adhoc Source:Pencilbox|JHub */"
        )
    elif env_type == "airflow":
        assert (
            q.split("\n")[0]
            == "/* User:test QueryHash:ae5631fb7d2d48609afa1589494d4e08 TaskID:dag_1234 Source:Pencilbox|Airflow */"
        )
    elif env_type == "unknown":
        assert (
            q.split("\n")[0]
            == f"/* User:test QueryHash:ae5631fb7d2d48609afa1589494d4e08 TaskID:adhoc Source:Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')} */"
        )

    assert result.fetchall() == [(1,)]

    # Test_5: Query via pandas
    df = pandas.read_sql_query(
        """
    -- single line in query
    /* multiline comment in query */
    select 1 as one where 1 = %(one)s
    """,
        con=con,
        params={"one": 1},
    )
    assert df["one"].tolist() == [1]

    # Test_6: Query containing random crap via pandas
    df = pandas.read_sql_query(
        """
    -- single line in query {test_string} {{%%test_string_1%%}} ///test_string_2/// \\\\test_string_3\\
    /* multiline comment in query {test_string} {{%%test_string_1%%}} ///test_string_2/// \\\\test_string_3\\ */
    select 1 as one where 1 = %(one)s
    """,
        con=con,
        params={"one": 1},
    )
    assert df["one"].tolist() == [1]

    # Test_7: Query containing keywords used in annotate function via pandas
    df = pandas.read_sql_query(
        """
    -- single line in query {statement}
    /* multiline comment in query {test_string} {{%%test_string_1%%}} ///test_string_2/// \\\\test_string_3\\ */
    select 1 as one where 1 = %(one)s
    """,
        con=con,
        params={"one": 1},
    )
    assert df["one"].tolist() == [1]


@patch("pencilbox.connections.get_secret", side_effect=get_secret)
def test_unknown_env(mock_get_secret):
    os.environ.pop("JUPYTERHUB_USER", None)
    os.environ["USER"] = "test"
    query_annotation_check("postgres", "unknown")
    query_annotation_check("mysql", "unknown")
    os.environ.pop("USER", None)
    _clear_cache()


@patch("pencilbox.connections.get_secret", side_effect=get_secret)
def test_airflow_env(mock_get_secret):
    os.environ["AIRFLOW_DAG_OWNER"] = "test"
    os.environ["AIRFLOW__KUBERNETES__DAGS_VOLUME_SUBPATH"] = "airflow-test/dags"
    os.environ["AIRFLOW_DAG_ID"] = "dag_1234"
    query_annotation_check("postgres", "airflow")
    query_annotation_check("mysql", "airflow")
    os.environ.pop("AIRFLOW_DAG_OWNER", None)
    os.environ.pop("AIRFLOW__KUBERNETES__DAGS_VOLUME_SUBPATH", None)
    os.environ.pop("AIRFLOW_DAG_ID", None)
    _clear_cache()


@patch("pencilbox.connections.get_secret", side_effect=get_secret)
def test_forecasting_dashboard_connection(mock_get_secret):
    """Test the forecasting dashboard connection configuration"""
    os.environ["USER"] = "test"

    # Test connection creation
    con = pb.get_connection("[Database] Forecasting Dashboard")

    # Test simple query
    result = con.execute("select 1")
    assert result.fetchall() == [(1,)]

    # Test that it's configured as postgres type with SSL
    conn_dict = pb.connections.get_conn_dict("[Database] Forecasting Dashboard")
    assert conn_dict["type"] == "postgres"

    os.environ.pop("USER", None)
    _clear_cache()


@patch("pencilbox.connections.get_secret", side_effect=get_secret)
def test_jhub_env(mock_get_secret):
    os.environ["JUPYTERHUB_USER"] = "test"
    query_annotation_check("postgres", "jhub")
    query_annotation_check("mysql", "jhub")
    os.environ.pop("JUPYTERHUB_USER", None)
    _clear_cache()
