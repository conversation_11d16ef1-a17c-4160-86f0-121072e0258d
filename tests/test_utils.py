"""Test Load Metadata to catalog functionality"""

import os
from pencilbox.utils import get_task_metadata


def test_get_task_metadata():
    os.environ.pop("AIRFLOW_DAG_OWNER", None)
    os.environ.pop("AIRFLOW_DAG_ID", None)
    os.environ.pop("AIRFLOW_DAG_RUN_ID", None)
    os.environ.pop("USER", None)

    os.environ["JUPYTERHUB_USER"] = "<EMAIL>"
    assert get_task_metadata() == {
        "user": "<EMAIL>",
        "task_id": "adhoc",
        "source": "Pencilbox|JHub",
    }

    os.environ.pop("JUPYTERHUB_USER", None)
    assert get_task_metadata() == {
        "user": "Unknown",
        "task_id": "adhoc",
        "source": f"Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')}",
    }

    os.environ["USER"] = "<EMAIL>"
    assert get_task_metadata() == {
        "user": "<EMAIL>",
        "task_id": "adhoc",
        "source": f"Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')}",
    }

    os.environ["AIRFLOW_DAG_OWNER"] = "<EMAIL>"
    os.environ["AIRFLOW_ENDPOINT"] = "https://airflow-common.grofer.io"
    assert get_task_metadata() == {
        "user": "<EMAIL>",
        "task_id": "adhoc",
        "run_id": "Unknown",
        "source": "Pencilbox|Airflow",
        "dag_link": "https://airflow-common.grofer.io/tree?dag_id=None",
    }

    os.environ["AIRFLOW_DAG_ID"] = "test_dag_id"
    os.environ["AIRFLOW_DAG_RUN_ID"] = "test_dag_run_id"
    assert get_task_metadata() == {
        "user": "<EMAIL>",
        "task_id": "test_dag_id",
        "run_id": "test_dag_run_id",
        "source": "Pencilbox|Airflow",
        "dag_link": "https://airflow-common.grofer.io/tree?dag_id=test_dag_id",
    }
