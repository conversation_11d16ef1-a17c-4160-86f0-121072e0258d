import pytest
import logging

from pencilbox.hooks import S3Hook


@pytest.fixture
def mocked_s3_calls(monkeypatch):
    def mock_init(*args, **kwargs):
        pass

    def mock_list_objects(*args, **kwargs):
        return [
            "source_prefix/object_1",
            "source_prefix/object_2",
        ]

    def mock_copy_object(*args, **kwargs):
        logging.info(
            f"Copying {kwargs.get('source_bucket')}/{kwargs.get('source_key')} to {kwargs.get('sink_bucket')}/{kwargs.get('sink_key')}..."
        )

    monkeypatch.setattr(S3Hook, "__init__", mock_init)
    monkeypatch.setattr(S3Hook, "list_objects", mock_list_objects)
    monkeypatch.setattr(S3Hook, "copy_object", mock_copy_object)


def test_copy_by_prefix_invalid_source_prefix(mocked_s3_calls):
    """
    Only source prefix does not end with a `/`
    """
    hook = S3Hook()
    with pytest.raises(ValueError):
        hook.copy_by_s3_prefix(
            "source_bucket", "source_prefix_no_slash", "sink_bucket", "sink_prefix_slash/"
        )


def test_copy_by_prefix_invalid_sink_prefix(mocked_s3_calls):
    """
    Only sink prefix does not end with a `/`
    """
    hook = S3Hook()
    with pytest.raises(ValueError):
        hook.copy_by_s3_prefix(
            "source_bucket", "source_prefix_slash/", "sink_bucket", "sink_prefix_no_slash"
        )


def test_copy_by_prefix_both_invalid_prefix(mocked_s3_calls):
    """
    No prefix ends with a `/`
    """
    hook = S3Hook()
    with pytest.raises(ValueError):
        hook.copy_by_s3_prefix(
            "source_bucket", "source_prefix_no_slash", "sink_bucket", "sink_prefix_no_slash"
        )


def test_copy_by_prefix_both_valid_prefix(mocked_s3_calls):
    """
    Both prefix end with a `/`. No error should be raised.
    """
    hook = S3Hook()
    hook.copy_by_s3_prefix(
        "source_bucket", "source_prefix_slash/", "sink_bucket", "sink_prefix_slash/"
    )


def test_copy_by_prefix_correct_keys_passed_to_copy_object(mocked_s3_calls, caplog):
    """
    Test if source and sink key names are being passed as expected by asserting logs
    """
    caplog.set_level(logging.INFO)

    hook = S3Hook()
    hook.copy_by_s3_prefix("source_bucket", "source_prefix/", "sink_bucket", "sink_prefix/")

    expected_logs = [
        (
            "root",
            logging.INFO,
            "Copying source_bucket/source_prefix/object_1 to sink_bucket/sink_prefix/object_1...",
        ),
        (
            "root",
            logging.INFO,
            "Copying source_bucket/source_prefix/object_2 to sink_bucket/sink_prefix/object_2...",
        ),
    ]
    assert caplog.record_tuples == expected_logs
