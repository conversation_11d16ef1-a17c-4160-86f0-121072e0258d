name: pencil-box-release

on:
  release:
    types: [published]
    tags: [ 'v*.*.*' ]

env:
  ENVIRONMENT: production

jobs:
  build:
    runs-on: [ blinkit-ci-action-runner-default ]
    steps:
      - name: Check out main repository
        uses: actions/checkout@v3
        with:
          ref: master

      - name: Install Docker Compose
        run: |
            sudo curl -L "https://github.com/docker/compose/releases/download/v2.3.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            docker-compose --version
        continue-on-error: false

      - name: Build docker images
        run: docker-compose -f docker-compose.release.yml build --force-rm --no-cache
        env:
          VAULT_URL: https://vault-ui-stage.grofer.io/
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_NAME: ${{ github.ref_name }}

      - name: Push to PiPy
        run: docker-compose -f docker-compose.release.yml run release_production
        env:
          VAULT_URL: https://vault-ui-stage.grofer.io/
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_NAME: ${{ github.ref_name }}

      - name: Docker Clean up
        run: docker-compose -f docker-compose.release.yml down --remove-orphans
        env:
          VAULT_URL: https://vault-ui-stage.grofer.io/
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_NAME: ${{ github.ref_name }}