name: pencil-box-build

on:
  pull_request:
    branches:
      - 'master'

jobs:
  build:
    name: repository-custom-required-check
    runs-on: [ blinkit-ci-action-runner-default ]
    steps:
      - name: Check out main repository
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Install Docker Compose
        run: |
            sudo curl -L "https://github.com/docker/compose/releases/download/v2.33.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            docker-compose --version
        continue-on-error: false

      - name: Build docker images
        run: docker-compose -f docker-compose.yml build --force-rm
        env:
          VAULT_URL: https://vault-ui-stage.grofer.io/
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}

      - name: Docker tests
        run: docker-compose -f docker-compose.yml run pytest
        env:
          VAULT_URL: https://vault-ui-stage.grofer.io/
          VAULT_SERVICE_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}

      - name: Docker Clean up
        run: docker-compose -f docker-compose.yml down --remove-orphans
        env:
          VAULT_URL: https://vault-ui-stage.grofer.io/
          VAULT_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
