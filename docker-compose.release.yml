version: '3'

services:

  release_production:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        VERSION: ${TAG_NAME} # this is set by <PERSON>
    command: python scripts/push_to_cheeseshop.py production
    environment:
    - VAULT_URL=${VAULT_URL}
    - VAULT_TOKEN=${VAULT_TOKEN}
    - VAULT_SERVICE_TOKEN=${VAULT_SERVICE_TOKEN}

  release_test:
    build:
      context: .
      dockerfile: ./Dockerfile
      args:
        VERSION: "v1.0.0" # this is a dummy version
    command: python scripts/push_to_cheeseshop.py test
    depends_on:
    - pypi_server
    environment:
    - VAULT_URL=${VAULT_URL}
    - VAULT_TOKEN=${VAULT_TOKEN}
    - VAULT_SERVICE_TOKEN=${VAULT_SERVICE_TOKEN}
    - PYPI_SERVER=http://pypi_server:8080/

  pypi_server:
    image: pypiserver/pypiserver:v1.4.2
    command: -P . -a .
    ports:
    - "8080:8080"
