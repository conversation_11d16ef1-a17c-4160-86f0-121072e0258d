FROM public.ecr.aws/zomato/python:3.9

# Possible Values : production, test
ARG ENVIORNMENT=production

ENV PYTHONFAULTHANDLER=1 \
  PYTHONUNBUFFERED=1 \
  # pip:
  PIP_NO_CACHE_DIR=off \
  PIP_DISABLE_PIP_VERSION_CHECK=on \
  # poetry:
  POETRY_VERSION=1.7.1 \
  POETRY_NO_INTERACTION=1 \
  POETRY_VIRTUALENVS_CREATE=false \
  POETRY_CACHE_DIR='/var/cache/pypoetry' \
  PATH="$PATH:/root/.local/bin"

RUN apt-get update && \
    apt-get install --no-install-recommends -y curl gcc libc-dev \
    # Installing `poetry` package manager:
    # https://github.com/python-poetry/poetry
    && curl -sSL 'https://install.python-poetry.org' | python - \
    && poetry --version \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY pyproject.toml poetry.lock ./

ARG VERSION=v0.1.0

RUN poetry lock --no-update --no-interaction
RUN poetry install --no-root $(if [ "$ENVIORNMENT" = 'production' ]; then echo '--no-dev'; fi)

COPY . /app

RUN poetry version $VERSION

RUN poetry build && pip install dist/*.whl
