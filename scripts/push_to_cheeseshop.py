import logging
import os
import sys
import time

import hvac
import requests


logging.basicConfig(format='%(asctime)s %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)


def get_hvac_client():
    client = hvac.Client(url=os.environ.get("VAULT_URL"))
    client.token = os.environ.get("VAULT_SERVICE_TOKEN")
    if not client.is_authenticated():
        client.auth.github.login(token=os.environ.get("VAULT_TOKEN"))
    if not client.is_authenticated():
        raise ValueError("Could not authenticate to Vault")
    return client


def wait_for_pypi_server(local_repo):
    if not local_repo:
        raise ValueError("PYPI_SERVER environment variable not set")

    for _ in range(10):
        try:
            requests.get(local_repo)
            logger.info("Local pypi server is up")
            return
        except requests.exceptions.ConnectionError:
            logger.info("Waiting for local server to start...")
            time.sleep(5)

    raise ValueError("Could not connect to local server")


def upload_to_pypi(repo_name, repo_host, repo_username, repo_password, healthcheck=False):
    if healthcheck:
        wait_for_pypi_server(repo_host)
    logger.info(f"Adding the {repo_name} pypi server ({repo_host}) to poetry config")
    os.system(f"poetry config repositories.{repo_name} {repo_host}")
    logger.info(f"Uploading to {repo_name} pypi server")
    os.system(f"poetry publish -r '{repo_name}' -u '{repo_username}' -p '{repo_password}'")
    logger.info(f"Uploaded to {repo_name} pypi server")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "production":
        client = get_hvac_client()
        logger.info("Fetching credentials from Vault")
        cheeseshop_credentials = client.read("dse/services/cheeseshop/credentials")["data"]
        upload_to_pypi(
            repo_name="grofers",
            repo_host=cheeseshop_credentials["host"],
            repo_username=cheeseshop_credentials["username"],
            repo_password=cheeseshop_credentials["password"],
            healthcheck=False,
        )
    else:
        upload_to_pypi(
            repo_name="local",
            repo_host=os.environ.get("PYPI_SERVER"),
            repo_username='dummy',
            repo_password='dummy',
            healthcheck=True,
        )
