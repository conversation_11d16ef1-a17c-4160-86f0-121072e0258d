[tool.poetry]
name = "pencilbox"
# This is a dummy version. It is replaced by the real version during the build
version = "v1.9.0"
description = "A pencilbox of utilities for your notebooks."
authors = ["DE Team <<EMAIL>>"]
readme = "README.md"

[[tool.poetry.source]]
name = "grofers-pypi"
url = "https://pypi.grofer.io/simple/"
priority = "default"

[tool.poetry.dependencies]
python = "^3.9"
decore = "~1.6.4"
confluent-kafka = {version = "1.8.2"}
PyHive = "0.6.4"
thrift = "0.15.0"
tenacity = "8.0.1"
gspread = "4.0.1"
slack_sdk = "3.34.0"
oauth2client = "4.1.3"
gspread-dataframe = "3.0.3"
sendgrid = "^6.9.7"
pinotdb = "^0.4.13"
gitpython = "3.1.26"
acryl-datahub = "********"
fastavro = "1.7.3"
pydantic = "2.2.1"
typing_extensions = "4.10.0"
simplejson = "3.19.2"
numpy = ">=1.20.0,<2.0"

[tool.poetry.dev-dependencies]
pytest = "6.2.5"
pytest-cov = "3.0.0"
pytest-runner = "5.3.1"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''
