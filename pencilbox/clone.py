import os
import re

from git import Repo, GitCommandError
from .connections import get_secret
from .utils import get_task_metadata
from .constants import PENCILBOX_CONFIG_VAULT_PATH, PENCILBOX_TOKENS_VAULT_PATH


def raise_exception(e):  # custom exception function to avoid exposing token
    warning_match = re.search(r"warning: (.+)", str(e))
    fatal_match = re.search(r"fatal: (.+)", str(e))
    raise Exception(
        f"Cloning repository failed.\nError: {warning_match.group(1)}\nFatal: {fatal_match.group(1)}"
    ) from None


def is_clone_allowed(repo_name, branch_to_fetch):
    user_email = get_task_metadata().get("user", "")
    user_based_allowed_repos_config = get_secret(PENCILBOX_CONFIG_VAULT_PATH).get(
        "user_based_allowed_repos", {}
    )
    allowed_repos = user_based_allowed_repos_config.get(user_email, [])
    allowed_branches = get_secret(PENCILBOX_CONFIG_VAULT_PATH).get("allowed_branches", [])
    if repo_name not in allowed_repos:
        raise Exception(f"You do not have permission to clone git repository {repo_name}.")
    if branch_to_fetch not in allowed_branches:
        raise Exception(
            f"You do not have permission to fetch branch {branch_to_fetch} of git repository {repo_name}."
        )


def clone_repo(repo_name, clone_at_path, branch_to_fetch="master"):
    vault_token = get_secret(PENCILBOX_TOKENS_VAULT_PATH)["vault_token"]
    repo_url = f"https://{vault_token}@github.com/grofers/{repo_name}"
    clone_at = f"{clone_at_path}/{repo_name}"

    # Validate if user is allowed to clone the requested repository
    is_clone_allowed(repo_name, branch_to_fetch)
    # Check if the directory already exists, if yes, delete it
    if os.path.exists(clone_at):
        raise Exception("Repository already exists.")
    # Clone the repository
    try:
        Repo.clone_from(repo_url, clone_at, branch=branch_to_fetch, single_branch=True)
    except GitCommandError as e:
        raise_exception(e)
