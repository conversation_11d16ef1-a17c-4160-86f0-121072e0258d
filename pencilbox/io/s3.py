# -*- coding: utf-8 -*-
from ..hooks import S3Hook


class LoadLocaltoS3(object):
    def __init__(self, filename=None, bucket=None, object_name=None):
        self.filename = filename
        self.bucket = bucket
        self.object_name = object_name

    def execute(self):
        s3_hook = S3Hook()
        s3_hook.load_file(self.filename, self.bucket, self.object_name)


def to_s3(filename, bucket, object_name):
    loader = LoadLocaltoS3(filename=filename, bucket=bucket, object_name=object_name)
    loader.execute()


class GetFilefromS3(object):
    def __init__(self, bucket=None, object_name=None, filename=None):
        self.bucket = bucket
        self.object_name = object_name
        self.filename = filename

    def execute(self):
        s3_hook = S3Hook()
        s3_hook.get_file(self.bucket, self.object_name, self.filename)


def from_s3(bucket, object_name, filename):
    loader = GetFilefromS3(bucket=bucket, object_name=object_name, filename=filename)
    loader.execute()
