# -*- coding: utf-8 -*-
import pandas
import gspread
import warnings
import gspread_dataframe as gd
import tenacity as tc

from gspread.exceptions import APIError
from oauth2client.service_account import ServiceAccountCredentials
from google.oauth2.service_account import Credentials

from ..connections import get_secret
from ..utils import get_task_metadata, extract_company, IS_JHUB
from ..constants import GSHEETS_TOKEN_VAULT_PATH

SCOPES = [
    "https://spreadsheets.google.com/feeds",
    "https://www.googleapis.com/auth/drive",
]

_delegated_gc_client = {}
_gc_client = {}
_retry_codes = [429, 500, 503]


def deprecation_warn(warn_type):
    if warn_type == "service_account":
        warnings.simplefilter("always", DeprecationWarning)
        warnings.warn(
            "Support for providing explicit service_account has been deprecated due to security purposes, and will be removed in upcoming versions.\nReading/writing to google sheets will be supported only for sheets where you have the required access",
            category=DeprecationWarning,
            stacklevel=2,
        )


def _delegated_cached_client(author_email, clear_cache):
    if clear_cache is False:
        gc = _delegated_gc_client[author_email]
        return gc
    else:
        raise KeyError("Invalidate Cache")


def delegated_gspread_client(author_email, clear_cache=False):
    try:
        gc = _delegated_cached_client(author_email, clear_cache)
    except KeyError:
        cred_dict = get_secret(GSHEETS_TOKEN_VAULT_PATH).get(extract_company(author_email))
        credentials = Credentials.from_service_account_info(cred_dict, scopes=SCOPES)
        delegated_credentials = credentials.with_subject(author_email)
        gc = gspread.Client(auth=delegated_credentials)
        _delegated_gc_client[author_email] = gc
    return gc


@tc.retry(
    retry=tc.retry_if_exception_type(tc.TryAgain),
    wait=tc.wait_exponential(),
    stop=tc.stop_after_attempt(5),
)
def get_gsheet(sheetid, sheetname, service_account="old_service_account", clear_cache=False):
    if service_account != "old_service_account":
        deprecation_warn("service_account")

    if IS_JHUB:
        raise Exception("Google sheet operations are disabled in JupyterHub")
    meta = get_task_metadata()
    author_email = meta.get("user")
    gc = delegated_gspread_client(author_email, clear_cache)

    try:
        wb = gc.open_by_key(sheetid)
        sh = wb.worksheet(sheetname)
    except APIError as e:
        exception_code = e.response.json().get("error", {}).get("code")
        if exception_code in _retry_codes:
            raise tc.TryAgain
        raise e
    return sh


@tc.retry(
    retry=tc.retry_if_exception_type(tc.TryAgain),
    wait=tc.wait_exponential(),
    stop=tc.stop_after_attempt(5),
)
def from_sheets(sheetid, sheetname, service_account="old_service_account", clear_cache=False):
    """Download a google sheet given the sheet_id and the sheet_name"""
    try:
        sh = get_gsheet(sheetid, sheetname, service_account, clear_cache)
        table_values = sh.get_all_values()
        df = pandas.DataFrame(table_values[1:], columns=table_values[0])
        return df
    except APIError as e:
        exception_code = e.response.json().get("error", {}).get("code")
        if exception_code in _retry_codes:
            raise tc.TryAgain
        raise e


@tc.retry(
    retry=tc.retry_if_exception_type(tc.TryAgain),
    wait=tc.wait_exponential(),
    stop=tc.stop_after_attempt(5),
)
def to_sheets(df, sheetid, sheetname, service_account="old_service_account", clear_cache=False):
    """Upload a Dataframe to Google sheets"""
    try:
        sh = get_gsheet(sheetid, sheetname, service_account, clear_cache)
        gd.set_with_dataframe(sh, df, resize=True)
        return "Updated sheet {}".format(sheetname)
    except APIError as e:
        exception_code = e.response.json().get("error", {}).get("code")
        if exception_code in _retry_codes:
            raise tc.TryAgain
        raise e
