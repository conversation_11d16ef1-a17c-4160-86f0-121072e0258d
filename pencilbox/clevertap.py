import pandas as pd

from collections import defaultdict
from datetime import datetime
from pytz import timezone
from uuid import uuid4

from .connections import get_connection
from .kafka import to_kafka
from .utils import IS_PROD_ENV, get_task_metadata
from .constants import (
    TRINO_CONNECTION_ID,
    Z_B_KAFKA_CONN_ID,
    DEFAULT_TENANT,
    CLEVERTAP_SINK_TOPICS,
)

CHUNK_SIZE = 500000


def _validate_user_input(df, task_id):
    """
    Validation checks:
    - check if segment name exists in registered segments (de_etls.clevertap_segments)
    - check if current task_id is a valid `sync_source`
    - check if current date is between `start_date` and `end_date`
    - check if df size is under `max_segment_push_size` defined
    - check if segment data type is same as registered `pandas_data_type`
    """
    all_columns = df.columns.values.tolist()
    if "user_id" not in all_columns:
        raise Exception("user_id column is missing. It is mandatory to upload user properties")

    segments = list(set(all_columns) - set(["user_id"]))
    segment_dtypes = df[segments].dtypes.to_dict()
    segments_str = ",".join([f"'{prop}'" for prop in segments])

    # not syncing user_ids which have all the segments as null
    df.dropna(subset=segments, inplace=True)
    column_count = df.count()

    print(f"Running validations over {segments}")
    conn = get_connection(TRINO_CONNECTION_ID)

    segment_details = pd.read_sql_query(
        f"""
        SELECT * FROM blinkit_iceberg.de_etls.clevertap_segments
        WHERE segment_name in ({segments_str})
        """,
        con=conn,
    )

    registered_segments = segment_details.set_index("segment_name").to_dict(orient="index")

    segment_validation_errors = defaultdict(list)
    current_date_str = datetime.now(timezone("Asia/Kolkata")).strftime("%Y-%m-%d")
    for segment in segments:
        if segment not in registered_segments.keys():
            segment_validation_errors[segment].append("- Segment not registered")
        else:
            segment_details = registered_segments[segment]
            if IS_PROD_ENV and not any(task_id in src for src in segment_details["sync_source"]):
                segment_validation_errors[segment].append(
                    f"- Sync source not registered. Segment can only be updated from {segment_details['sync_source']}"
                )

            if segment_details["start_date"] > current_date_str:
                segment_validation_errors[segment].append(
                    f"- Segment not active. Start date: {segment_details['start_date']}"
                )

            if segment_details["end_date"] < current_date_str:
                segment_validation_errors[segment].append(
                    f"- Segment expired on {segment_details['end_date']}"
                )

            # segment being pushed can have upto 10% more columns than registered
            if segment_details["max_segment_push_size"] * (1.1) <= column_count[segment]:
                segment_validation_errors[segment].append(
                    f"- Segment size of {column_count[segment]} exceeds max registered limit of {segment_details['max_segment_push_size']}"
                )

            if segment_details["pandas_data_type"] == "STRING":
                if segment_dtypes[segment] != "object":
                    segment_validation_errors[segment].append(
                        f"- Segment data type mismatch. Registered is `{segment_details['pandas_data_type']}` but passed in dataframe as `{str(segment_dtypes[segment]).upper()}`"
                    )
                elif not df[segment].apply(lambda x: isinstance(x, str)).all():
                    segment_validation_errors[segment].append(
                        "- All values in segment should be of type string"
                    )
            elif str(segment_dtypes[segment]).upper() != segment_details["pandas_data_type"]:
                segment_validation_errors[segment].append(
                    f"- Segment data type mismatch. Registered is `{segment_details['pandas_data_type']}` but passed in dataframe as `{str(segment_dtypes[segment]).upper()}`"
                )

    return df, segment_validation_errors


def push_to_clevertap(df, tenant, task_id, to_kafka_kwargs):
    df = df.set_index("user_id")
    segment_columns = list(df.columns)
    df[["values", "keys"]] = df.apply(
        lambda row: (
            {
                "anonymousId": str(uuid4()),
                "type": "identify",
                "userId": str(row.name),
                "context": {"segments": row.to_dict()},
                "metadata": get_task_metadata(),
            },
            f"{task_id}:user:{row.name}",
        ),
        result_type="expand",
        axis=1,
    )
    topic = CLEVERTAP_SINK_TOPICS[tenant]

    to_kafka(
        conn_id=Z_B_KAFKA_CONN_ID,
        topic=topic,
        df=df.drop(segment_columns, axis=1),
        value_columns=["values"],
        key_columns=["keys"],
        **to_kafka_kwargs,
    )


def to_clevertap(df, tenant=DEFAULT_TENANT, *args, **kafka_kwargs):
    """
    validate user input & convert df to valid clevertap json
    push to clevertap kafka topic if enviroment = prod

    Args:
        df (pandas.Dataframe): Dataframe with user_id and segments to be pushed to Clevertap
    """
    to_kafka_kwargs = {
        "batch.size": 10000,
        "linger.ms": 500,
        "compression.type": "gzip",
    }
    if tenant not in CLEVERTAP_SINK_TOPICS.keys():
        raise Exception(
            f"`{tenant}` is not a valid tenant for to_clevertap. Please use a valid tenant."
        )

    to_kafka_kwargs.update(kafka_kwargs)

    task_id = get_task_metadata()["task_id"].rsplit("_v", 1)[0]

    df, segment_validation_errors = _validate_user_input(df, task_id)

    if segment_validation_errors:
        print("\n=====================================")
        print("Validation Error(s) Report:")
        print("=====================================")
        for segement_name, errors in segment_validation_errors.items():
            print(segement_name)
            for error in errors:
                print(f"{error}")
        print("=====================================\n")
        raise Exception(
            "Validation failed for one or more segments. Please fix the above errors and try again. Documentation: https://github.com/grofers/airflow-dags/blob/master/dags/de/personas/etl/clevertap_sync/README.rst"
        )
    else:
        print("Validation checks passed!")
        if not IS_PROD_ENV:
            print(
                "DATA CAN NOT be pushed from staging environment but it has been validated. Please schedule this code on airflow to push data to CleverTap."
            )
            return

        print("Pushing data to Clevertap")
        for start in range(0, len(df), CHUNK_SIZE):
            end = min(start + CHUNK_SIZE, len(df))

            print(f"Pushing chunk containing rows {start} to {end}")
            push_to_clevertap(df[start:end], tenant, task_id, to_kafka_kwargs)
            print(f"Chunk ({start} - {end}) pushed successfully")

        print("Data pushed to Clevertap successfully")
