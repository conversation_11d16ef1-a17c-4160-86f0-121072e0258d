import os
import re
import warnings
from typing import List
from itertools import chain
from decore.io.trino.to_trino import to_trino as decore_to_trino
from decore.io.trino.utils import get_columns_info_from_sql as decore_get_columns_info_from_sql
from decore.vault import get_secret

from .datahub import to_datahub, fetch_dataset_custom_properties
from .observability import record_observability_metrics
from .utils import (
    get_task_metadata,
    IS_JHUB,
    IS_STAGING_ENV,
    extract_iceberg_metadata,
    get_trino_source,
    IS_AIRFLOW_ARS_ENV,
)
from .constants import (
    PENCILBOX_CONFIG_VAULT_PATH,
    JHUB_EKS_VAULT_DEFAULT_ROLE,
    AIRFLOW_EKS_VAULT_DEFAULT_ROLE,
    JHUB_CONNECTION_MAP_VAULT_PATH,
    TRINO_OBSERVABILITY_TOPIC,
    TRINO_METRICS_KAFKA_CLIENT_ID,
    DEFAULT_VAULT_K8S_AUTH_MOUNT,
)


class ToTrinoValidator(object):
    def __init__(self, is_staging_env, allowed_schemas=None, catalog_schemas=None):
        self.allowed_schemas = allowed_schemas
        self.is_staging_env = is_staging_env
        self.catalog_schemas = catalog_schemas
        self.internal_flags = ["skip_partition"]
        if not os.environ.get("VAULT_K8S_AUTH_ROLE"):
            os.environ["VAULT_K8S_AUTH_ROLE"] = (
                JHUB_EKS_VAULT_DEFAULT_ROLE if IS_JHUB else AIRFLOW_EKS_VAULT_DEFAULT_ROLE
            )
        if not os.environ.get("VAULT_K8S_AUTH_MOUNT"):
            os.environ["VAULT_K8S_AUTH_MOUNT"] = DEFAULT_VAULT_K8S_AUTH_MOUNT

    def __call__(self, to_trino):
        def wrapped_function(data_obj, schema_name, table_name, *args, **kwargs):
            self.allowed_schemas = get_secret(PENCILBOX_CONFIG_VAULT_PATH)[
                "allowed_schemas_write"
            ].split(",")
            self.catalog_schemas = get_secret(PENCILBOX_CONFIG_VAULT_PATH)[
                "catalog_required_schemas"
            ].split(",")
            self.validate_internal_flags(**kwargs)
            self.validate_catalog_required_schema(schema_name, **kwargs)
            if not self.is_staging_env:
                return to_trino(data_obj, schema_name, table_name, *args, **kwargs)
            self.validate_allowed_schema_write(schema_name)
            self.validate_table(table_name)
            return to_trino(data_obj, schema_name, table_name, *args, **kwargs)

        return wrapped_function

    def validate_table(self, table_name):
        regex = re.compile(r"^[0-9_]|[.@!#$%^&*()<>?/\|}{~:]|^\w{0,4}$")
        if regex.search(table_name):
            raise Exception(
                "Please follow the rules given below to create a table."
                "\n1. Length of table name should be atleast 5."
                "\n2. First character of table name should be an alphabet."
                "\n3. Table name can consist of alphabets, numeric values and '_' only"
            )

    def validate_table_description(self, **kwargs):
        if not kwargs.get("table_description"):
            raise ValueError(
                "Missing table_description! Please provide a valid table description to proceed. "
                "Learn more: https://data.grofer.io/how-to/trino/create-derived-tables-over-trino"
            )

    def validate_column_description(self, **kwargs):
        if not(("column_dtypes" in kwargs) ^ ("column_descriptions" in kwargs)):
            raise ValueError(
                "Please pass either column_dtypes or column_descriptions! (exactly one of them)"
                "Please provide valid column descriptions to proceed. "
                "Learn more: https://data.grofer.io/how-to/trino/create-derived-tables-over-trino"
            )
        param = "column_dtypes" if kwargs.get("column_dtypes") else "column_descriptions"
        if not isinstance(kwargs.get(param), list):
            raise ValueError(
                f"Expected {param} to be a list, got {type(kwargs.get(param))} instead."
            )
        for column_dtype in kwargs.get(param, []):
            if not column_dtype.get("description"):
                raise ValueError(
                    f"Missing column description for {column_dtype['name']}. "
                    "Learn more: https://data.grofer.io/how-to/trino/create-derived-tables-over-trino"
                )

    def validate_allowed_schema_write(self, schema_name):
        if schema_name not in self.allowed_schemas:
            raise Exception(f"The tables inside the '{schema_name}' cannot be updated from jHub.")

    def validate_catalog_required_schema(self, schema_name, **kwargs):
        # Catalog updation necessary
        if schema_name in self.catalog_schemas:
            self.validate_table_description(**kwargs)
            self.validate_column_description(**kwargs)

    def validate_internal_flags(self, **kwargs):

        user_flags = kwargs.get('flags', None)
        if user_flags:
            user_flag_keys = set(user_flags.keys())
            internal_flag_keys = set(self.internal_flags)
            conflicting_flags = user_flag_keys.intersection(internal_flag_keys)
            
            if conflicting_flags:
                raise ValueError(
                    f"The following internal flags cannot be passed directly by users: {', '.join(conflicting_flags)}. "
                    f"These flags are automatically managed by the system."
                )


@ToTrinoValidator(is_staging_env=IS_STAGING_ENV)
def to_trino(data_obj, schema_name, table_name, *args, **kwargs):
    kwargs.setdefault("conn_parameters", {})
    kwargs["environment"] = "prod"
    kwargs["task_metadata"] = get_task_metadata()
    kwargs["conn_parameters"]["session_props"] = {}
    flags = {}
    if IS_AIRFLOW_ARS_ENV:
        flags["skip_partition"] = True
    
    if kwargs.get("enable_parallel_partition_overwrite"):
        warnings.warn(
            "Passing 'enable_parallel_partition_overwrite' as a separate parameter is deprecated. "
            "Please pass it inside the 'flags' dictionary instead: flags={'enable_parallel_partition_overwrite': True}",
            DeprecationWarning,
            stacklevel=2
        )
        flags["enable_parallel_partition_overwrite"] = kwargs.pop("enable_parallel_partition_overwrite")
    
    user_flags = kwargs.pop("flags", None)
    if user_flags:
        flags.update(user_flags)
    kwargs["flags"] = flags
    if IS_STAGING_ENV:
        kwargs["conn_parameters"] = {
            "user_impersonation": True,
            "user": os.environ.get(
                "JUPYTERHUB_USER", os.environ.get("AIRFLOW_DAG_OWNER", "unknown")
            ),
            "query_source": "jhub",
            "vault_path": JHUB_CONNECTION_MAP_VAULT_PATH,
        }
        kwargs["environment"] = "staging"
    else:
        kwargs["iceberg_metadata"] = extract_iceberg_metadata(
            dataset_custom_properties=fetch_dataset_custom_properties(
                schema_name, table_name, data_platform="trino"
            )
        )
        trino_source = get_trino_source(kwargs["task_metadata"]["task_id"])
        kwargs["conn_parameters"]["query_source"] = trino_source if trino_source else kwargs["conn_parameters"].get("query_source", "etl_airflow")
    # Use decore's to_trino function to upload data to trino
    default_return = (None, {}, {})
    return_value = decore_to_trino(data_obj, schema_name, table_name, *args, **kwargs)
    error, trino_column_dtypes, etl_metric_data, *rest = chain(return_value, default_return)
    # pushing etl_event logs to kafka
    if kwargs["environment"] == "prod" and etl_metric_data:
        record_observability_metrics(
            etl_metric_data,
            TRINO_METRICS_KAFKA_CLIENT_ID,
            TRINO_OBSERVABILITY_TOPIC,
        )

    if error:
        raise Exception(error)

    param = "column_dtypes" if kwargs.get("column_dtypes") else "column_descriptions"
    if "table_description" in kwargs and param in kwargs:
        warning_flag = False
        for column_dtype in trino_column_dtypes:
            if column_dtype not in kwargs[param]:
                warnings.warn(
                    f"Column {column_dtype['name']} is missing in {param}. ",
                    FutureWarning,
                )
                warning_flag = True
                kwargs[param].append({
                    "name": column_dtype["name"],
                    "type": column_dtype["type"],
                    "description": ""
                })
        for column_dtype in kwargs[param]:
            if not column_dtype.get("description"):
                warnings.warn(
                    "Missing column description.\nAdding column description is a GOOD practice",
                    FutureWarning,
                )
                warning_flag = True
        if warning_flag:
            return
        if kwargs["environment"] == "prod":
            if schema_name not in ["interim", "playground"]:
                custom_properties = kwargs.get("properties", {})
                if etl_metric_data:
                    custom_properties.update(
                        extract_iceberg_metadata(
                            etl_metric_data=etl_metric_data,
                            dataset_custom_properties=kwargs["iceberg_metadata"],
                        )
                    )
                # Upload metadata of the table to datahub
                to_datahub(
                    schema_name=schema_name,
                    table_name=table_name,
                    column_dtypes=kwargs["column_dtypes"],
                    table_description=kwargs["table_description"],
                    data_platform="trino",
                    docs=kwargs.get("docs", []),
                    properties=custom_properties,
                    primary_keys=kwargs.get("primary_key", []),
                    partition_keys=kwargs.get("partition_key", []),
                )

def get_columns_info_from_sql(sql_query: str) -> List[dict]:
    return decore_get_columns_info_from_sql(sql_query)