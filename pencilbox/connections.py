# -*- coding: utf-8 -*-

import warnings
import os
import hashlib
import hvac
import sqlalchemy
import urllib.parse
import requests
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from sqlalchemy import event
from sendgrid import SendGridAPIClient
from .utils import get_task_metadata, IS_JHUB, DEFAULT_K8S_AUTH_ROLE, get_trino_source
from .trino_utils import allowed_session_props
from .constants import (
    DEFAULT_EKS_SA_TOKEN_PATH,
    DEFAULT_VAULT_K8S_AUTH_MOUNT,
    SLACK_TOKEN_VAULT_PATH,
    SENDGRID_API_TOKEN_VAULT_PATH,
    JHUB_CONNECTION_MAP_VAULT_PATH,
    PROD_CONNECTION_MAP_VAULT_PATH,
    PENCILBOX_CONFIG_VAULT_PATH,
    FEATURE_STORE_BATCH_CONN_ID,
    FEATURE_STORE_BISTRO_VAULT_PATH,
)

_client = {"slack": {}}
_conn_cache = {"secret": {}, "engine": {}, "session_props": {}}


def _clear_cache():
    _conn_cache["secret"] = {}
    _conn_cache["engine"] = {}
    _conn_cache["session_props"] = {}


def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    statement = (
        "/*"
        " User:{user}"
        f" QueryHash:{hashlib.md5(statement.encode('utf-8')).hexdigest()}"
        " TaskID:{task_id}"
        " Source:{source}"
        " */\n {statement}"
    ).format(**get_task_metadata(), statement=statement)

    return statement, parameters


def get_hvac_client():
    try:
        client = _client["hvac"]
    except KeyError:
        client = hvac.Client(url=os.environ.get("VAULT_URL"))

    if not client.is_authenticated():
        client.token = os.environ.get("VAULT_SERVICE_TOKEN")
        if not client.is_authenticated():
            client = _authenticate_using_sa(client)
            if not client.is_authenticated():
                client = _authenticate_using_github_token(client)
        if not client.is_authenticated():
            raise ValueError("Could not authenticate to Vault")
        _client["hvac"] = client

    return client


def _authenticate_using_github_token(client) -> hvac.Client:
    vault_github_token = os.environ.get("VAULT_TOKEN")
    if vault_github_token:
        print("Authenticating with vault using github token")
        client.auth.github.login(token=vault_github_token)

    return client


def _authenticate_using_sa(client) -> hvac.Client:
    vault_k8s_token_path = os.environ.get("VAULT_K8S_TOKEN_PATH", DEFAULT_EKS_SA_TOKEN_PATH)
    vault_k8s_auth_role = os.environ.get("VAULT_K8S_AUTH_ROLE", DEFAULT_K8S_AUTH_ROLE)
    vault_k8s_auth_mount = os.environ.get("VAULT_K8S_AUTH_MOUNT", DEFAULT_VAULT_K8S_AUTH_MOUNT)

    if os.path.isfile(vault_k8s_token_path) and vault_k8s_auth_role:
        print("Authenticating with vault using service account")
        with open(vault_k8s_token_path, "r") as f:
            client.auth.kubernetes.login(
                role=vault_k8s_auth_role, jwt=f.read().strip(), mount_point=vault_k8s_auth_mount
            )

    return client


def get_secret(path):
    try:
        secret = _conn_cache["secret"][path]
    except KeyError:
        client = get_hvac_client()
        _conn_cache["secret"][path] = client.read(path)["data"]
        secret = _conn_cache["secret"][path]

    return secret


def get_slack_client(user):
    def _slack_client():
        hvac_client = get_hvac_client()
        secrets = hvac_client.read(SLACK_TOKEN_VAULT_PATH)["data"]
        slack_token = secrets[user]
        client = WebClient(token=slack_token)
        _client["slack"][user] = client
        return client

    try:
        client = _client["slack"][user]
        if not client.auth_test().get("ok"):
            client = _slack_client()
    except (KeyError, SlackApiError):
        client = _slack_client()
    return client


def get_sendgrid_client():
    try:
        sendgrid_client = _client["sendgrid"]
    except KeyError:
        SendGrid_API_TOKEN = get_secret(SENDGRID_API_TOKEN_VAULT_PATH)["token"]
        sendgrid_client = SendGridAPIClient(SendGrid_API_TOKEN)
    return sendgrid_client


def deprecation_warn(conn_map, conn_name):
    if conn_name.islower():
        for connection_name in conn_map:
            if conn_map[conn_name] == conn_map[connection_name] and not connection_name.islower():
                new_conn_name = connection_name
                warnings.simplefilter("always", DeprecationWarning)
                warnings.warn(
                    "Connection name '{conn_name}' is deprecated.\nPlease use '{new_conn_name}' instead of '{conn_name}'.".format(
                        conn_name=conn_name, new_conn_name=new_conn_name
                    ),
                    category=DeprecationWarning,
                    stacklevel=2,
                )
                break


def get_conn_dict(path, topic=None):

    if IS_JHUB:
        is_user_allowed(path)
        conn_map = get_secret(JHUB_CONNECTION_MAP_VAULT_PATH)
    else:
        conn_map = get_secret(PROD_CONNECTION_MAP_VAULT_PATH)
    deprecation_warn(conn_map, path)

    # Special handling for feature store batch connection with bistro topics for migration, to be removed in future
    if path == FEATURE_STORE_BATCH_CONN_ID and topic and _is_bistro_topic(topic):
        vault_path = FEATURE_STORE_BISTRO_VAULT_PATH
    else:
        vault_path = conn_map[path]

    conn_dict = get_secret(vault_path)

    return conn_dict


def _is_bistro_topic(topic):
    """
    Check if a topic is a bistro topic.

    Args:
        topic (str): The Kafka topic name

    Returns:
        bool: True if the topic is a bistro topic
    """
    return topic.startswith("bistro.")


def is_valid_connection(engine):
    try:
        engine.connect()
    except Exception:
        return False
    return True


def is_end_of_life(connection_dict, conn_name):
    if connection_dict.get("end_of_life"):
        warnings.simplefilter("always", DeprecationWarning)
        warnings.warn(
            "Connection name '{conn_name}' has reached its end of life and no longer supported.".format(
                conn_name=conn_name
            ),
            category=DeprecationWarning,
            stacklevel=2,
        )
        return True
    return False


def is_user_allowed(conn_id):
    user_email = get_task_metadata().get("user", "")
    allowed_users_config = get_secret(PENCILBOX_CONFIG_VAULT_PATH)["allowed_get_connection_users"]
    restricted_connections = list(allowed_users_config.keys())
    if conn_id in restricted_connections:
        allowed_users = [user.strip() for user in allowed_users_config[conn_id].split(",")]
        if user_email not in allowed_users:
            raise Exception(
                f"Either {conn_id} connection is deprecated or you do not have permission to access this connection."
            )


def get_connection(conn_id, conn_parameters={}, create_engine=True):  # noqa : C901
    if create_engine:
        session_props = conn_parameters.get("session_props", {})
        try:
            
            if not IS_JHUB and (_conn_cache["session_props"][conn_id] != session_props):
                _clear_cache()
            conn = _conn_cache["engine"][conn_id]
            if IS_JHUB:
                is_user_allowed(conn_id)
                conn_map = _conn_cache["secret"][JHUB_CONNECTION_MAP_VAULT_PATH]
            else:
                conn_map = _conn_cache["secret"][PROD_CONNECTION_MAP_VAULT_PATH]
            deprecation_warn(conn_map, conn_id)
        except Exception:
            conn_dict = get_conn_dict(conn_id)

            kwargs = {}
            if conn_dict["type"] == "postgres":
                kwargs = {"connect_args": {"sslmode": "prefer"}}
            elif conn_dict["type"] == "mysql":
                kwargs = {"pool_pre_ping": True}
            elif conn_dict["type"] in {"trino", "presto"}:
                kwargs = {"connect_args": {"protocol": "https"}}
                
                session_props_filtered = {
                    key: value
                    for key, value in session_props.items()
                    if key in allowed_session_props and allowed_session_props[key](value)
                }
                if not IS_JHUB:
                    kwargs['connect_args']["session_props"] = session_props_filtered

                
            elif conn_dict["type"] == "pinot":
                kwargs = {}
            else:
                raise ValueError("Unknown connection type")

            if is_end_of_life(conn_dict, conn_id):
                _conn_cache["engine"][conn_id] = None
                return None

            if IS_JHUB and conn_dict["type"] in {"trino", "presto"}:
                username = conn_dict.get("user", conn_dict.get("username"))
                password = conn_dict.get("password")
                if f"{username}:{password}@" not in conn_dict["uri"]:
                    raise Exception(
                        f"Password mismatched in the vault for connection alias {conn_id}"
                    )
                
                session = requests.Session()
                session.auth = requests.auth.HTTPBasicAuth(username, password)
                kwargs["connect_args"]["requests_session"] = session
                kwargs["connect_args"]["source"] = "jhub"
                
                uri = conn_dict["uri"].replace(
                    f"{username}:{password}@", f"{os.environ.get('JUPYTERHUB_USER', 'Unknown')}@"
                )
                _conn_cache["engine"][conn_id] = sqlalchemy.create_engine(uri, **kwargs)
                
                # _conn_cache["session_props"][conn_id] = kwargs["connect_args"]["session_props"]   Removing session cache in JHUB since we have not implemented session props 
            else:
                uri = conn_dict["uri"]
                if conn_dict["type"] in {"trino", "presto"}:
                    trino_source = get_trino_source(get_task_metadata().get("task_id", ""))
                    username = conn_dict.get("user", conn_dict.get("username"))
                    password = conn_dict.get("password")
                    kwargs["connect_args"]["source"] = (
                        trino_source if trino_source else conn_parameters.get("query_source", "common_airflow")
                    )
                    _conn_cache["session_props"][conn_id] = kwargs["connect_args"]["session_props"]
                    kwargs["connect_args"]["username"] = username
                    kwargs["connect_args"]["password"] = password
                    uri = uri.replace(
                        f"{username}:{password}@", ""
                    )
                _conn_cache["engine"][conn_id] = sqlalchemy.create_engine(
                    uri, **kwargs
                )
                

            if not is_valid_connection(_conn_cache["engine"][conn_id]):
                password = conn_dict["password"]
                parsed_password = urllib.parse.quote_plus(password)
                if f":{password}@" not in conn_dict["uri"]:
                    raise Exception(
                        f"Password mismatched in the vault for connection alias {conn_id}"
                    )
                uri = conn_dict["uri"].replace(f":{password}@", f":{parsed_password}@")
                _conn_cache["engine"][conn_id] = sqlalchemy.create_engine(uri, **kwargs)
                if not is_valid_connection(_conn_cache["engine"][conn_id]):
                    raise Exception(
                        f"Failed to make connection. Please check the credentials for connection alias {conn_id}"
                    )

            conn = _conn_cache["engine"][conn_id]

            event.listen(conn, "before_cursor_execute", before_cursor_execute, retval=True)
    else:
        try:
            conn = _conn_cache["secret"][conn_id]
            if IS_JHUB:
                is_user_allowed(conn_id)
                conn_map = _conn_cache["secret"][JHUB_CONNECTION_MAP_VAULT_PATH]
            else:
                conn_map = _conn_cache["secret"][PROD_CONNECTION_MAP_VAULT_PATH]
            deprecation_warn(conn_map, conn_id)
        except Exception:
            dictfilt = lambda x, y: dict([(i, x[i]) for i in x if i in set(y)])  # noqa: E731
            wanted_keys = ("host", "port", "user", "password", "dbname")
            conn_dict = get_conn_dict(conn_id)

            if is_end_of_life(conn_dict, conn_id):
                _conn_cache["engine"][conn_id] = None
                return None

            _conn_cache["secret"][conn_id] = dictfilt(conn_dict, wanted_keys)
            conn = _conn_cache["secret"][conn_id]

    return conn
