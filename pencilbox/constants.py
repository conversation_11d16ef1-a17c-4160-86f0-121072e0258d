PENCILBOX_CONFIG_VAULT_PATH = "dse/pencilbox/config"
PENCILBOX_TOKENS_VAULT_PATH = "dse/pencilbox/envs"
JHUB_CONNECTION_MAP_VAULT_PATH = "dse/pencilbox/key_map_jhub"
PROD_CONNECTION_MAP_VAULT_PATH = "dse/pencilbox/key_map"
GSHEETS_TOKEN_VAULT_PATH = "data/google-sheets/destination-sync-delegated"

TRINO_CONNECTION_ID = "[Warehouse] Trino"
FORECASTING_DASHBOARD_CONNECTION_ID = "[Database] Forecasting Dashboard"

Z_B_KAFKA_CONN_ID = "[Kafka] zomato-blinkit"
TEST_KAFKA_CONN_ID = "[Kafka] test-data-common"
TRINO_OBSERVABILITY_TOPIC = "observability.trino_etl_metrics_topic"
KAFKA_OBSERVABILITY_TOPIC = "observability.to_kafka_metrics_topic"
DEFAULT_KAFKA_CLIENT_ID = "pencilbox-default"
KAKFA_METRICS_KAFKA_CLIENT_ID = "pencilbox-to-kafka-metrics"
TRINO_METRICS_KAFKA_CLIENT_ID = "pencilbox-default"
DEFAULT_TENANT = "blinkit"
BISTRO_TENANT = "bistro"
CLEVERTAP_SINK_TOPICS = {
    DEFAULT_TENANT: "blinkit.ols.flash_gateway",
    BISTRO_TENANT: "bistro.ct.flash_gateway",
}

DEFAULT_EKS_SA_TOKEN_PATH = "/run/secrets/kubernetes.io/serviceaccount/token"
DEFAULT_VAULT_K8S_AUTH_MOUNT = "eks"
JHUB_EKS_VAULT_DEFAULT_ROLE = "dse-jhub-default-role"
AIRFLOW_EKS_VAULT_DEFAULT_ROLE = "dse-airflow-default-role"

SLACK_TOKEN_VAULT_PATH = "dse/services/slack/tokens"
SLACK_BOT_NAME = "bl-analytics-bot"
P0_ALERT_SLACK_CHANNEL = "bl-data-alerts-p0"
P1_ALERT_SLACK_CHANNEL = "bl-data-alerts-p1"
P2_ALERT_SLACK_CHANNEL = "bl-data-alerts-p2"
TEST_SLACK_CHANNEL = "bl-data-slack-testing"

SENDGRID_API_TOKEN_VAULT_PATH = "data/sendgrid"
DEFAULT_FROM_EMAIL_ID = "<EMAIL>"

DATAHUB_KAFKA_CONFIG_VAULT_PATH = "dse/services/datahub/kafka_broker"
DATAHUB_KAFKA_SCHEMA_REGISTRY_VAULT_PATH = "dse/services/datahub/kafka_schema_registry"
DATAHUB_API_VAULT_PATH = "dse/services/datahub/api_key"

FEATURE_STORE_BATCH_CONN_ID = "[Kafka] feature-store-batch"
FEATURE_STORE_REALTIME_CONN_ID = "[Kafka] feature-store-realtime"

REQUIRED_FEATURE_STORE_HEADERS = ["X-Tenant-Id", "X-Tenant-Namespace", "X-Api-Key"]

FEATURE_STORE_TOPIC_TO_TENANT_VAULT_PATH = "data/services/feature-store/prod/topic-to-tenant"
FEATURE_STORE_TENANT_VAULT_PATH = "data/services/feature-store/prod/tenants"
FEATURE_STORE_API_KEY_VAULT_PATH = "data/services/feature-store/prod/api-key"

# Special vault path for feature store batch connection with bistro topics
FEATURE_STORE_BISTRO_VAULT_PATH = "data/kafka/blinkit-prod-cep-data-platform"
