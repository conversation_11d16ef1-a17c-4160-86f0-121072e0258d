import os
import re
import warnings
from decore.io.redshift import to_redshift as decore_to_redshift
from decore.vault import get_secret
from .datahub import to_datahub
from .utils import get_task_metadata
from .constants import PENCILBOX_CONFIG_VAULT_PATH


class ToRedshiftValidator(object):
    def __init__(
        self,
        user_source,
        user_email,
        allowed_schemas=None,
        catalog_schemas=None,
        allowed_tables_override=None,
        allowed_schemas_override=None,
    ):
        self.allowed_schemas = allowed_schemas
        self.user_source = user_source
        self.user_email = user_email
        self.catalog_schemas = catalog_schemas
        self.allowed_tables_override = allowed_tables_override
        self.allowed_schemas_override = allowed_schemas_override

    def __call__(self, to_redshift):
        def wrapped_function(data_obj, schema_name, table_name, *args, **kwargs):
            redshift_config = get_secret(PENCILBOX_CONFIG_VAULT_PATH)
            self.allowed_schemas = redshift_config["allowed_schemas_write"].split(",")
            self.catalog_schemas = redshift_config["catalog_required_schemas"].split(",")
            self.allowed_tables_override = redshift_config["allowed_new_redshift_tables"].split(",")
            self.allowed_schemas_override = redshift_config["allowed_new_redshift_schemas"].split(
                ","
            )
            self.allowed_schemas_override = redshift_config["allowed_new_redshift_schemas"].split(
                ","
            )
            self.validate_catalog_required_schema(schema_name, **kwargs)
            kwargs["allow_new_tables"] = self.allow_new_table_creation(table_name, schema_name)
            if self.user_source != "Pencilbox|JHub":
                return to_redshift(data_obj, schema_name, table_name, *args, **kwargs)
            self.is_user_allowed(self.user_email)
            self.validate_allowed_schema_write(schema_name)
            self.validate_table(table_name)
            return to_redshift(data_obj, schema_name, table_name, *args, **kwargs)

        return wrapped_function

    def validate_table(self, table_name):
        regex = re.compile("^[0-9_]|[.@!#$%^&*()<>?/\|}{~:]|^\w{0,4}$")  # noqa W605
        if regex.search(table_name):
            raise Exception(
                "Please follow the rules given below to create a table."
                "\n1. Length of table name should be atleast 5."
                "\n2. First character of table name should be an alphabet."
                "\n3. Table name can consist of alphabets, numeric values and '_'"
            )

    def allow_new_table_creation(self, table_name, schema_name):
        if (
            schema_name in self.allowed_schemas_override
            or f"{schema_name}.{table_name}" in self.allowed_tables_override
        ):
            return True
        return False

    def validate_table_description(self, **kwargs):
        if not kwargs.get("table_description"):
            raise ValueError(
                "Missing table_description! Please provide a valid table description to proceed. "
                "Learn more: https://data.grofer.io/tools/pencilbox#load-data-from-a-csv-or-pandas-dataframe-to-redshift"
                "Missing table_description! Please provide a valid table description to proceed. "
                "Learn more: https://data.grofer.io/tools/pencilbox#load-data-from-a-csv-or-pandas-dataframe-to-redshift"
            )

    def validate_column_description(self, **kwargs):
        for column_dtype in kwargs.get("column_dtypes", []):
            if not column_dtype.get("description"):
                raise ValueError(
                    f"Missing column description for {column_dtype['name']}. "
                    "Learn more: https://data.grofer.io/tools/pencilbox#load-data-from-a-csv-or-pandas-dataframe-to-redshift"
                )

    def validate_allowed_schema_write(self, schema_name):
        if schema_name not in self.allowed_schemas:
            raise Exception(f"The tables inside the '{schema_name}' cannot be updated from jHub.")

    def validate_catalog_required_schema(self, schema_name, **kwargs):
        # Catalog updation necessary
        if schema_name in self.catalog_schemas:
            self.validate_table_description(**kwargs)
            self.validate_column_description(**kwargs)

    def is_user_allowed(self, user_email):
        allowed_users_config = get_secret(PENCILBOX_CONFIG_VAULT_PATH)
        allowed_users = [
            user.strip() for user in allowed_users_config["allowed_to_redshift_users"].split(",")
        ]
        if user_email not in allowed_users:
            raise Exception(
                "pb.to_redshift() is either deprecated or you do not have permission to use it. Please move your use-cases to Trino."
            )


@ToRedshiftValidator(
    user_source=get_task_metadata().get("source", ""),
    user_email=get_task_metadata().get("user", ""),
)
def to_redshift(data_obj, schema_name, table_name, *args, **kwargs):
    # Use decore's to_redshift function to upload data to redshift
    decore_to_redshift(data_obj, schema_name, table_name, *args, **kwargs)

    if "table_description" in kwargs and "column_dtypes" in kwargs:
        for column_dtype in kwargs["column_dtypes"]:
            if not column_dtype.get("description"):
                warnings.warn(
                    "Missing column description.\nAdding column description is a GOOD practice",
                    FutureWarning,
                )
                return

        # Upload metadata of the table to datahub
        to_datahub(
            schema_name=schema_name,
            table_name=table_name,
            column_dtypes=kwargs["column_dtypes"],
            table_description=kwargs["table_description"],
            data_platform="redshift",
            docs=kwargs.get("docs", []),
            properties=kwargs.get("properties", {}),
            primary_keys=kwargs.get("primary_key", []),
        )
