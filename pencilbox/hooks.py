# -*- coding: utf-8 -*-
import boto3
import logging
import psycopg2

from confluent_kafka import Producer
from contextlib import closing

from .connections import get_conn_dict, get_connection
from .constants import DEFAULT_KAFKA_CLIENT_ID


class S3Hook(object):
    def __init__(self, s3_client=None):
        self.s3_client = s3_client if s3_client else boto3.client("s3")

    def get_credentials(self):
        creds = ""
        session = boto3.Session()
        credentials = session.get_credentials()

        # Credentials are refreshable, so accessing your access key / secret key
        # separately can lead to a race condition. Use this to get an actual matched
        # set.
        credentials = credentials.get_frozen_credentials()
        access_key = credentials.access_key
        secret_key = credentials.secret_key

        creds = "aws_access_key_id={0};aws_secret_access_key={1}".format(access_key, secret_key)

        return creds

    def load_file(self, filename, bucket, object_name):
        self.s3_client.upload_file(filename, bucket, object_name)

    def get_file(self, bucket, object_name, filename):
        self.s3_client.download_file(bucket, object_name, filename)

    def list_objects(self, bucket, prefix):
        """
        Returns all objects in the bucket that share the provided prrefix
        """
        objects = [
            key["Key"]
            for key in self.s3_client.list_objects(Bucket=bucket, Prefix=prefix)["Contents"]
        ]
        return objects

    def copy_object(self, source_bucket, source_key, sink_bucket, sink_key):
        """
         Copy an s3 object with a given key from one location to another within
         s3, `sink_key` determines the object's key in the new location.

        Example:
             source_bucket = "test"
             source_key = "utils/myfile.csv"

             sink_bucket = "prod"
             sink_key = "final_file.csv"

             test/utils/myfile.csv ----> prod/final_file.csv
        """
        source_params = {
            "Bucket": source_bucket,
            "Key": source_key,
        }

        logging.info(f"Copying {source_bucket}/{source_key} to {sink_bucket}/{sink_key}...")
        self.s3_client.copy(source_params, sink_bucket, sink_key)

    def copy_by_s3_prefix(self, source_bucket, source_prefix, sink_bucket, sink_prefix):
        """
        Copy multiple s3 objects with a common prefix from one s3 location to another.
        """
        if not (source_prefix.endswith("/") and sink_prefix.endswith("/")):
            raise ValueError(
                "Source and sink prefix names must end with a `/`. "
                "Use s3_hook.copy_object() to copy a standalone object."
            )

        objects_to_copy = self.list_objects(source_bucket, source_prefix)

        for object in objects_to_copy:
            prefix_removed_source_object = object.removeprefix(source_prefix)
            sink_key = f"{sink_prefix}{prefix_removed_source_object}"
            self.copy_object(
                source_bucket=source_bucket,
                source_key=object,
                sink_bucket=sink_bucket,
                sink_key=sink_key,
            )


class PgHook(object):
    def __init__(self, conn_id):
        self.conn_id = conn_id

    def get_conn(self):
        conn_args = get_connection(self.conn_id, create_engine=False)
        return psycopg2.connect(**conn_args)

    def get_autocommit(self, conn):
        return getattr(conn, "autocommit", False)

    def set_autocommit(self, conn, autocommit):
        conn.autocommit = autocommit

    def get_records(self, sql, parameters=None):
        with closing(self.get_conn()) as conn:
            with closing(conn.cursor()) as cur:
                if parameters is not None:
                    cur.execute(sql, parameters)
                else:
                    cur.execute(sql)
                return cur.fetchall()

    def run(self, sql, autocommit=False, parameters=None):
        if isinstance(sql, str):
            sql = [sql]

        with closing(self.get_conn()) as conn:
            self.set_autocommit(conn, autocommit)

            with closing(conn.cursor()) as cur:
                for s in sql:
                    if parameters is not None:
                        cur.execute(s, parameters)
                    else:
                        cur.execute(s)

            if not self.get_autocommit(conn):
                conn.commit()


class KafkaHook(object):
    def __init__(self, conn_id, *args, topic=None, **kwargs):
        """
        Initialize the KafkaHook with the specified Kafka broker(s).

        The hook supports three authentication modes based on the connection configuration:
        1. SASL_SSL: When 'bootstrap.servers.ssl' is present - uses SCRAM-SHA-512 with SSL
        2. SASL_PLAINTEXT: When 'password' is present but 'bootstrap.servers.ssl' is not - uses SCRAM-SHA-512 without SSL
        3. No authentication: When neither SSL nor password is configured

        Special handling: For feature-store-batch connection with bistro topics,
        uses 'data/kafka/blinkit-prod-cep-data-platform' vault path.

        Args:
            conn_id (str): The connection ID for Kafka configuration.
            topic (str, optional): The Kafka topic name for special routing logic.
            bootstrap_servers (str): Comma-separated list of Kafka broker addresses.
            client_id (str): The client identifier used to identify this client with the Kafka cluster. Default is "pencilbox-default".
            linger_time (int): The number of milliseconds a producer is allowed to wait for accumulating messages before sending them. Default is 0.
            batch_size (int): The number of bytes to wait for before sending messages to the Kafka broker. Default is 16384 bytes.
            compression_type (str): The compression type for messages. Default is "snappy".
        """
        kafka_hook_secrets = get_conn_dict(conn_id, topic)

        if "bootstrap.servers.ssl" in kafka_hook_secrets:
            producer_config = {
                "bootstrap.servers": kafka_hook_secrets["bootstrap.servers.ssl"],
                "security.protocol": "SASL_SSL",
                "sasl.mechanisms": "SCRAM-SHA-512",
                "sasl.username": kafka_hook_secrets["username"],
                "sasl.password": kafka_hook_secrets["password"],
                "client.id": DEFAULT_KAFKA_CLIENT_ID,
                "linger.ms": 0,
                "batch.size": 16384,
                "on_delivery": self.callback,
                "compression.type": "snappy",
            }
        elif "password" in kafka_hook_secrets:
            # SASL authentication without SSL
            producer_config = {
                "bootstrap.servers": kafka_hook_secrets["bootstrap.servers"],
                "security.protocol": "SASL_PLAINTEXT",
                "sasl.mechanisms": "SCRAM-SHA-512",
                "sasl.username": kafka_hook_secrets["username"],
                "sasl.password": kafka_hook_secrets["password"],
                "client.id": DEFAULT_KAFKA_CLIENT_ID,
                "linger.ms": 0,
                "batch.size": 16384,
                "on_delivery": self.callback,
                "compression.type": "snappy",
            }
        else:
            producer_config = {
                "bootstrap.servers": kafka_hook_secrets["bootstrap.servers"],
                "client.id": DEFAULT_KAFKA_CLIENT_ID,
                "linger.ms": 0,
                "batch.size": 16384,
                "on_delivery": self.callback,
                "compression.type": "snappy",
            }

        producer_config.update(kwargs)

        self.producer = Producer(producer_config)

    # TODO: make call_back function configurable
    def callback(self, err, msg):
        if err is not None:
            logging.warn(f"Failed to deliver message with error: {str(err)}")

    def produce_message(self, topic, value, key=None, headers=None):
        """
        Produce a message to the specified Kafka topic.

        Args:
            topic (str): The Kafka topic to which the message will be sent.
            key (str or None): The message key (can be None).
            value (str or bytes): The message value.
        """

        self.producer.produce(topic, key=key, value=value, headers=headers)
        self.producer.poll(0)

    def flush_messages(self):
        self.producer.flush()
