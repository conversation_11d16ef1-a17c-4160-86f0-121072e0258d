# -*- coding: utf-8 -*-
import os
import base64
import warnings
from typing import List
from pencilbox.connections import get_sendgrid_client
from pencilbox.constants import DEFAULT_FROM_EMAIL_ID
from sendgrid.helpers.mail import (
    Attachment,
    Disposition,
    FileContent,
    FileName,
    Mail,
)


def remove_empty_string(param, param_name):
    empty_string_in_param = '' in param
    if empty_string_in_param:
        warnings.warn(
            f"Parameter '{param_name}' in function 'send_email' has empty string. Please remove empty string from the parameter '{param_name}'.",
            category=UserWarning,
            stacklevel=2,
        )
        param = list(filter(None, param))
    return param


def send_email(
    from_email: str,
    to_email: List[str],
    subject: str,
    html_content: str,
    files: List[str] = [],
    cc: List[str] = [],
    bcc: List[str] = [],
    reply_to: str = None,
    **kwargs
) -> None:
    if to_email and not isinstance(to_email, list):
        raise Exception(f"Datatype of Parameter 'to_email' can only be list. You have passed {type(to_email)}")
    if files and not isinstance(files, list):
        raise Exception(f"Datatype of Parameter 'files' can only be list. You have passed {type(files)}")
    if cc and not isinstance(cc, list):
        raise Exception(f"Datatype of Parameter 'cc' can only be list. You have passed {type(cc)}")
    if bcc and not isinstance(bcc, list):
        raise Exception(f"Datatype of Parameter 'bcc' can only be list. You have passed {type(bcc)}")
    
    to_email = remove_empty_string(to_email, "to_email")
    message: Mail = Mail(
        from_email=DEFAULT_FROM_EMAIL_ID,
        to_emails=to_email,
        subject=subject,
        html_content=html_content,
    )
    message.reply_to = reply_to or from_email

    if cc:
        cc = list(set(cc) - set(to_email))
        cc = remove_empty_string(cc, "cc")
        message.cc = cc
    if bcc:
        bcc = list(set(bcc) - set(to_email) - set(cc))
        bcc = remove_empty_string(bcc, "bcc")
        message.bcc = bcc

    if files:
        attachment_files = []
        for fname in files or []:
          attachment_files.append(build_local_file_attachment(fname))
        message.attachment = attachment_files
    
    if "JUPYTERHUB_USER" in os.environ:
        home_path = os.environ.get("HOME", "/home/<USER>")
        file_name = "sample_email.html"
        file_path = os.path.join(home_path, file_name)
        with open(file_path, "w") as file:
            file.write(html_content)
        print(f"Sample email template has been saved in the home directory: {home_path}, with file name: {file_name}.")
        print("========================================================================")
        raise Exception(f"No email sent as `send_email()` is disabled in JupyterHub.")
    else:
        sendgrid_client = get_sendgrid_client()
        sendgrid_client.send(message)


def build_local_file_attachment(file_on_disk) -> Attachment:
    with open(file_on_disk, "rb") as f:
        data = f.read()
        f.close()
    encoded = base64.b64encode(data).decode()
    attachment = Attachment()
    attachment.file_content = FileContent(encoded)
    attachment.file_name = FileName(file_on_disk)
    attachment.disposition = Disposition("attachment")
    return attachment
