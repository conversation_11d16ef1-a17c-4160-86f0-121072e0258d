import simplejson as json
from confluent_kafka import KafkaException
from datetime import datetime
import pandas as pd
from pandas.core.series import Series
import warnings
import pencilbox as pb
import tempfile
import os
import yaml
import re

from .hooks import KafkaHook
from .observability import record_observability_metrics
from .utils import IS_PROD_ENV, get_task_metadata, IS_PREP_ENV, IS_JHUB, ToKafkaObservabilityFields
from .constants import (
    TEST_KAFKA_CONN_ID,
    KAFKA_OBSERVABILITY_TOPIC,
    KAKFA_METRICS_KAFKA_CLIENT_ID,
    FEATURE_STORE_BATCH_CONN_ID,
    FEATURE_STORE_REALTIME_CONN_ID,
    REQUIRED_FEATURE_STORE_HEADERS,
    FEATURE_STORE_TOPIC_TO_TENANT_VAULT_PATH,
    FEATURE_STORE_TENANT_VAULT_PATH,
    FEATURE_STORE_API_KEY_VAULT_PATH,
)


def _topic_exists(producer, topic):
    metadata = producer.list_topics(timeout=5)
    return metadata.topics.get(topic) is not None


def _convert_final_val(value):
    if type(value) == str:
        return value
    else:
        return json.dumps(value, ignore_nan=True)


# temporary function to add sourceID to FS payloads. This will need to be reverted once sourceID is expected in message header in FS v2
def _enrich_message(value, **kwargs):
    if kwargs["conn_id"] in [
        FEATURE_STORE_BATCH_CONN_ID,
        FEATURE_STORE_REALTIME_CONN_ID,
    ]:
        # Get topic-to-tenant mapping from vault
        tenant_info = pb.get_secret(FEATURE_STORE_TOPIC_TO_TENANT_VAULT_PATH)

        # Check if topic exists in the mapping
        if kwargs["topic_name"] in tenant_info:
            task_id = kwargs["task_metadata"].get("task_id", "")
            run_id = kwargs["task_metadata"].get("run_id", "")
            value["sourceID"] = "#".join([task_id, run_id])
    return value


def _get_key_value(df_row, key_columns=None, value_columns=None):
    key, value = None, None
    if key_columns:
        key = df_row[key_columns[0]] if len(key_columns) == 1 else df_row[key_columns].to_dict()
    if value_columns:
        value = (
            df_row[value_columns[0]] if len(value_columns) == 1 else df_row[value_columns].to_dict()
        )
    else:
        value = df_row[0] if len(df_row) == 1 else df_row.to_dict()
    return key, value


def validate_headers(headers):
    """
    Validates that headers are in the correct format: List of tuples [(key, value)].

    Args:
        headers (list): List of tuples representing headers.

    Raises:
        TypeError: If headers are not in the correct format.
        ValueError: If duplicate headers exist.
    """
    if not isinstance(headers, list):
        raise TypeError("Invalid headers format. Expecting a list of (key, value) tuples.")

    seen_keys = set()
    for header in headers:
        if not isinstance(header, tuple) or len(header) != 2:
            raise TypeError(f"Invalid header entry: {header}. Expecting (key, value) tuple.")

        key, value = header

        if not isinstance(key, str) or not isinstance(value, str):
            raise TypeError(
                f"Header keys and values must be strings. Found: {type(key)}, {type(value)}"
            )

        if key in seen_keys:
            raise ValueError(f"Duplicate header key found: {key}")

        seen_keys.add(key)

    return headers  # Return the validated headers list


def validate_feature_store_headers(headers, topic, tenant):
    """
    Validates Feature Store specific headers.

    Args:
        headers (list): List of validated headers.
        topic (str): The Kafka topic to determine the tenant.
        tenant (str): The tenant identifier.

    Returns:
        list: A validated list of headers.

    Raises:
        ValueError: If any required headers are missing or incorrect.
    """

    # Convert headers list into a dictionary for easy validation
    headers_dict = dict(headers)

    # Check if all required headers are present
    missing_headers = [h for h in REQUIRED_FEATURE_STORE_HEADERS if h not in headers_dict]
    if missing_headers:
        raise ValueError(f"Missing required headers: {', '.join(missing_headers)}")

    # Retrieve the correct Tenant ID from the secret store
    expected_tenant_ids = pb.get_secret(FEATURE_STORE_TENANT_VAULT_PATH)
    expected_tenant_id = expected_tenant_ids.get(tenant)  # Extract tenant-specific ID

    if not expected_tenant_id:
        raise ValueError(f"Failed to retrieve expected tenant ID for {tenant}.")

    # Validate the provided X-Tenant-Id against the expected ID
    if headers_dict["X-Tenant-Id"] != expected_tenant_id:
        raise ValueError(f"Invalid X-Tenant-Id: {headers_dict['X-Tenant-Id']} for tenant {tenant}.")

    # Ensure X-Tenant-Namespace is always "features"
    if headers_dict["X-Tenant-Namespace"] != "features":
        raise ValueError(
            f"Invalid X-Tenant-Namespace: {headers_dict['X-Tenant-Namespace']}. It must be 'features'."
        )

    return headers  # Return headers (no encoding done here)


def add_default_feature_store_headers(headers, topic, tenant):
    """
    Adds default Feature Store headers if they're not provided by the user.

    Args:
        headers (list): List of existing headers.
        topic (str): The Kafka topic to determine the tenant.
        tenant (str): The tenant identifier.

    Returns:
        list: Headers list with default values added for missing required headers.
    """
    # Convert headers list into a dictionary for easy manipulation
    headers_dict = dict(headers)

    # Retrieve the correct Tenant ID from the secret store
    expected_tenant_ids = pb.get_secret(FEATURE_STORE_TENANT_VAULT_PATH)
    expected_tenant_id = expected_tenant_ids.get(tenant)

    # Retrieve the default API key from the secret store
    default_api_key = pb.get_secret(FEATURE_STORE_API_KEY_VAULT_PATH).get("default")

    # Add default values for missing headers
    if "X-Tenant-Id" not in headers_dict and expected_tenant_id:
        headers.append(("X-Tenant-Id", expected_tenant_id))

    if "X-Tenant-Namespace" not in headers_dict:
        headers.append(("X-Tenant-Namespace", "features"))

    if "X-Api-Key" not in headers_dict:
        headers.append(("X-Api-Key", default_api_key))

    return headers


def _valid_feature_store_message(value):
    is_valid = True
    errors = []
    if not value.get("blinkID"):
        is_valid = False
        errors.append("blinkID is required but not found")
    if not isinstance(value.get("blinkID"), str):
        is_valid = False
        errors.append(f"blinkID expected str, but found {type(value.get('blinkID'))}")

    if value.get("dataType"):
        if not isinstance(value.get("dataType"), str):
            is_valid = False
            errors.append(f"dataType expected str, but found {type(value.get('dataType'))}")
        if value.get("dataType") not in ["MAP"]:
            is_valid = False
            errors.append(f"dataType can be `MAP`, but found {value.get('dataType')}")

    if not value.get("context"):
        is_valid = False
        errors.append("context is required but not found")
    if value.get("context") and not isinstance(value.get("context"), str):
        is_valid = False
        errors.append(f"context expected str, but found {type(value.get('context'))}")

    if value.get("ttl") and not isinstance(value.get("ttl"), int):
        is_valid = False
        errors.append(f"ttl expected int, but found {type(value.get('ttl'))}")

    if value.get("expiryEpoch") and not isinstance(value.get("expiryEpoch"), int):
        is_valid = False
        errors.append(f"expiryEpoch expected int, but found {type(value.get('expiryEpoch'))}")

    if value.get("loadType"):
        if not isinstance(value.get("loadType"), str):
            is_valid = False
            errors.append(f"loadType expected str, but found {type(value.get('loadType'))}")
        if value.get("loadType") not in ["INSERT", "UPDATE"]:
            is_valid = False
            errors.append(
                f"loadType can be `INSERT` or `UPDATE`, but found {value.get('loadType')}"
            )

    if not value.get("contextProperties"):
        is_valid = False
        errors.append("contextProperties is required but not found")
    if value.get("contextProperties") and not isinstance(value.get("contextProperties"), dict):
        is_valid = False
        errors.append(
            f"contextProperties expected dict, but found {type(value.get('contextProperties'))}"
        )

    return is_valid, "\n".join(errors)


class ToKafkaValidator(object):
    def __call__(self, to_kafka):
        def wrapped_function(
            conn_id,
            topic,
            df=None,
            key_columns=None,
            value_columns=None,
            headers=None,
            *args,
            **kwargs,
        ):

            headers = headers or []

            # Validate headers for ALL messages (ensures correct format)
            validated_headers = validate_headers(headers)

            # Feature Store specific validation
            if conn_id in [
                FEATURE_STORE_BATCH_CONN_ID,
                FEATURE_STORE_REALTIME_CONN_ID,
            ]:

                tenant_info = pb.get_secret(FEATURE_STORE_TOPIC_TO_TENANT_VAULT_PATH)

                if not tenant_info:
                    raise ValueError(f"Invalid topic: {topic}. Cannot determine tenant.")

                tenant = tenant_info[topic].get("tenant")

                headers_dict = dict(validated_headers)

                # Check if all required headers are present
                all_headers_present = all(
                    header in headers_dict for header in REQUIRED_FEATURE_STORE_HEADERS
                )

                if all_headers_present:
                    # If all headers are present, validate them
                    validated_headers = validate_feature_store_headers(
                        validated_headers, topic, tenant
                    )
                else:
                    # If any headers are missing, add default headers
                    validated_headers = add_default_feature_store_headers(
                        validated_headers, topic, tenant
                    )

            # Attach validated headers
            headers = validated_headers

            if kwargs.get("values", []) or kwargs.get("keys", []):
                warnings.simplefilter("always", DeprecationWarning)
                warnings.warn(
                    message="`values` and `keys` are deprecated as arguments to `to_kafka`; use `df` instead.",
                    category=DeprecationWarning,
                    stacklevel=2,
                )
                keys, values = kwargs.get("keys", []), kwargs.get("values", [])
                self.validate_key_value(keys, values)
                df = pd.DataFrame({"values": values})
                if keys:
                    df["keys"] = keys
                    key_columns, value_columns = ["keys"], ["values"]
                    kwargs.pop("keys")
                kwargs.pop("values")

            self.validate_df(df, key_columns, value_columns)
            if IS_JHUB:
                key, value = _get_key_value(
                    Series(df.values[0], df.columns), key_columns, value_columns
                )
                headers = headers  # Get validated headers from kwargs
                print(
                    f"Sample Record to be pushed: \n"
                    f"Key: {_convert_final_val(key)} \n"
                    f"Value: {_convert_final_val(value)} \n"
                    f"Headers: {headers}\n"
                )

            self.validate_environment()
            if conn_id in [
                FEATURE_STORE_BATCH_CONN_ID,
                FEATURE_STORE_REALTIME_CONN_ID,
            ]:
                self.validate_feature_store_messages(df, key_columns, value_columns, topic)
            return to_kafka(
                conn_id, topic, df, key_columns, value_columns, headers, *args, **kwargs
            )

        return wrapped_function

    def validate_key_value(self, keys, values):
        if values and not isinstance(values, list):
            raise TypeError(
                f"Unsupported type {type(values)} for argument `values`. Expecting a list"
            )
        if keys and not isinstance(keys, list):
            raise TypeError(
                f"Unsupported type {type(values)} for argument `keys`. Expecting a list"
            )
        if keys and len(keys) != len(values):
            raise Exception(
                f"Number of keys ({len(keys)}) is not equal to number of values ({len(values)})"
            )
        if len(values) <= 0:
            raise Exception("Number of values should be greater than 0")

    def validate_environment(self):
        if IS_JHUB or (not IS_PROD_ENV and not IS_PREP_ENV):
            raise Exception(
                "DATA CANNOT be pushed from Jhub. To test please schedule a DAG on airflow-prep to push data to test-kafka."
            )

    def validate_df(self, df, key_columns, value_columns):
        if df is None:
            raise AttributeError("Argument `df` cannot be None")
        if not isinstance(df, pd.DataFrame):
            raise TypeError(
                f"Unsupported type {type(df)} for argument `df`. Expecting a Pandas dataframe"
            )
        if key_columns:
            if not isinstance(key_columns, list):
                raise TypeError(
                    f"Unsupported type {type(key_columns)} for argument `key_columns`. Expecting list"
                )
            if set(key_columns) - set(df.columns):
                raise Exception(
                    f"{set(key_columns) - set(df.columns)} column not present in Dataframe"
                )
        if value_columns:
            if not isinstance(value_columns, list):
                raise TypeError(
                    f"Unsupported type {type(value_columns)} for argument `value_columns`. Expecting list"
                )
            if set(value_columns) - set(df.columns):
                raise Exception(
                    f"{set(value_columns) - set(df.columns)} column not present in Dataframe"
                )
        if len(df) <= 0:
            raise Exception("Number of rows should be greater than 0")

    def validate_feature_store_messages(self, df, key_columns, value_columns, topic):

        for idx, row in df.iterrows():
            key, value = _get_key_value(row, key_columns, value_columns)
            valid, error_reason = _valid_feature_store_message(value)
            if not valid:
                feature_store_message_error = f"Push failed due to incorrect feature store message schema. Reason: {error_reason}. Sample failed message: {_convert_final_val(value)}."
                logs_dict = {
                    ToKafkaObservabilityFields.TASK_METADATA.value: get_task_metadata(),
                    ToKafkaObservabilityFields.TOPIC_NAME.value: topic,
                    ToKafkaObservabilityFields.KEY_PASSED.value: True if key_columns else False,
                    ToKafkaObservabilityFields.ENVIRONMENT.value: "prod"
                    if IS_PROD_ENV
                    else "staging",
                    ToKafkaObservabilityFields.MESSAGE_COUNT.value: len(df),
                    ToKafkaObservabilityFields.SAMPLE_MESSAGE.value: _convert_final_val(value),
                    ToKafkaObservabilityFields.START_TIMESTAMP.value: datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                }
                logs_dict[ToKafkaObservabilityFields.END_TIMESTAMP.value] = datetime.now().strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                logs_dict[
                    ToKafkaObservabilityFields.ERROR.value
                ] = f"Push failed due to incorrect feature store message schema. Reason: {error_reason}."
                record_observability_metrics(
                    logs_dict,
                    KAKFA_METRICS_KAFKA_CLIENT_ID,
                    KAFKA_OBSERVABILITY_TOPIC,
                )
                raise Exception(feature_store_message_error)

        return


@ToKafkaValidator()
def to_kafka(
    conn_id, topic, df=None, key_columns=None, value_columns=None, headers=None, *args, **kwargs
):
    """
    Uploads data to kafka topic

    Args:
        conn_id (str): The Kafka connection ID for making connection to broker
        topic (topic): The Kafka topic in which data would be pushed
        df (pandas.Dataframe): Dataframe to be pushed to kafka
        key_columns (List[str]): The list of columns in Dataframe to be considered for making the record key
        value_columns (List[str]): The list of columns in Dataframe to be considered for making the record value
    """
    task_metadata = get_task_metadata()
    validated_headers = headers or []  # Headers are already validated in the wrapper

    logs_dict = {
        ToKafkaObservabilityFields.TASK_METADATA.value: task_metadata,
        ToKafkaObservabilityFields.TOPIC_NAME.value: topic,
        ToKafkaObservabilityFields.KEY_PASSED.value: True if key_columns else False,
        ToKafkaObservabilityFields.ENVIRONMENT.value: "prod" if IS_PROD_ENV else "staging",
        ToKafkaObservabilityFields.MESSAGE_COUNT.value: len(df),
        ToKafkaObservabilityFields.SAMPLE_MESSAGE.value: _convert_final_val(
            _get_key_value(Series(df.values[0], df.columns), key_columns, value_columns)[1]
        ),
        ToKafkaObservabilityFields.START_TIMESTAMP.value: datetime.now().strftime(
            "%Y-%m-%d %H:%M:%S"
        ),
    }

    if IS_PREP_ENV:
        conn_id = TEST_KAFKA_CONN_ID  # Replace connection id with Test Kafka ID

    error = None
    err_msg = None

    encoded_headers = [(key, value.encode("utf-8")) for key, value in validated_headers]
    # code for which errors needs to be recorded go inside try catch block
    try:
        if not kwargs.get("client.id"):
            kwargs["client.id"] = f"pencilbox-to-kafka-{task_metadata['task_id']}"

        hook = KafkaHook(conn_id=conn_id, topic=topic, **kwargs)

        if IS_PROD_ENV and not _topic_exists(hook.producer, topic):
            error = "TOPIC_NOT_FOUND"
            raise Exception(
                f"Topic {topic} does not exist. Please make sure the topic exist before pushing data to it."
            )

        count = 0
        for idx, row in df.iterrows():
            key, value = _get_key_value(row, key_columns, value_columns)
            value = _enrich_message(
                value, conn_id=conn_id, topic_name=topic, task_metadata=task_metadata
            )
            hook.produce_message(
                topic=topic,
                key=_convert_final_val(key).encode("utf-8") if key else None,
                value=_convert_final_val(value).encode("utf-8"),
                headers=encoded_headers,
            )
            count += 1
            if IS_PREP_ENV and count >= 10:
                print(f"Pushed first 10 messages to {topic} in test-data-common")
                break

        hook.flush_messages()
        logs_dict[ToKafkaObservabilityFields.END_TIMESTAMP.value] = datetime.now().strftime(
            "%Y-%m-%d %H:%M:%S"
        )
    except KafkaException as e:
        error = f"KAFKA_EXCEPTION.{e.args[0].code()}"
        err_msg = f"Failed to produce data to kafka with error: {e}"
    except Exception as e:
        error = error or "UNKNOWN_EXCEPTION"
        err_msg = f"Failed to produce data to kafka with error: {e}"

    logs_dict[ToKafkaObservabilityFields.ERROR.value] = error

    record_observability_metrics(
        logs_dict,
        KAKFA_METRICS_KAFKA_CLIENT_ID,
        KAFKA_OBSERVABILITY_TOPIC,
    )

    if err_msg:
        raise Exception(err_msg)
