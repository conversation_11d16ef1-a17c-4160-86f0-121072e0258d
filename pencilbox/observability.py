import json
import logging

from .connections import get_secret
from .hooks import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .constants import PENCILBOX_CONFIG_VAULT_PATH


def record_observability_metrics(metrics_dict, client_id, topic_id):
    try:
        value = json.dumps(metrics_dict).encode("utf-8")
        conf = get_secret(PENCILBOX_CONFIG_VAULT_PATH)
        kafka_hook = KafkaHook(
            conn_id=conf["observability.kafka_conn_id"],
            **{"client.id": client_id},
        )
        kafka_hook.produce_message(
            topic=conf[topic_id],
            value=value,
        )
        kafka_hook.flush_messages()
    except Exception as e:
        logging.warn(f"Failed to record observability metrics with error: {e}")
