from datetime import datetime
import json
import os
import pytz
import tenacity
from typing import List
from urllib import parse
import requests
import warnings

from decore.vault import get_secret
from .slack import send_slack_message
from .utils import get_task_metadata, get_timestamp, IS_PROD_ENV, IS_AIRFLOW_ARS_ENV
from .constants import (
    PENCILBOX_CONFIG_VAULT_PATH,
    DATAHUB_KAFKA_CONFIG_VAULT_PATH,
    DATAHUB_KAFKA_SCHEMA_REGISTRY_VAULT_PATH,
    DATAHUB_API_VAULT_PATH,
    P2_ALERT_SLACK_CHANNEL
)

from datahub.emitter.mce_builder import (
    make_data_platform_urn,
    make_dataset_urn,
    make_user_urn,
    make_domain_urn,
)
from datahub.emitter.mcp import MetadataChangeProposalWrapper
from datahub.emitter.kafka_emitter import <PERSON>hubKafkaEmitter, KafkaEmitterConfig
from datahub.metadata.schema_classes import DomainsClass

# Imports for metadata model classes
from datahub.metadata.schema_classes import (
    AuditStampClass,
    DateTypeClass,
    OtherSchemaClass,
    SchemaFieldClass,
    SchemaFieldDataTypeClass,
    SchemaMetadataClass,
    StringTypeClass,
    NumberTypeClass,
    BooleanTypeClass,
    RecordTypeClass,
    ArrayTypeClass,
    MapTypeClass,
    BytesTypeClass,
    InstitutionalMemoryClass,
    InstitutionalMemoryMetadataClass,
    DatasetPropertiesClass,
    OwnershipTypeClass,
    OwnerClass,
    OwnershipClass,
    DatasetKeyClass,
    TimeTypeClass,
)

DAG_OWNER = os.environ.get(
    "JUPYTERHUB_USER", os.environ.get("AIRFLOW_DAG_OWNER", "<EMAIL>")
)

TRINO_DATAHUB_MAPPING = {
    "BOOLEAN": BooleanTypeClass,
    "TINYINT": NumberTypeClass,
    "SMALLINT": NumberTypeClass,
    "INT": NumberTypeClass,
    "INTEGER": NumberTypeClass,
    "BIGINT": NumberTypeClass,
    "REAL": NumberTypeClass,
    "DOUBLE": NumberTypeClass,
    "DECIMAL": NumberTypeClass,
    "VARCHAR": StringTypeClass,
    "CHAR": StringTypeClass,
    "VARBINARY": BytesTypeClass,
    "JSON": StringTypeClass,
    "DATE": DateTypeClass,
    "TIME": TimeTypeClass,
    "TIMESTAMP": TimeTypeClass, 
    "ARRAY": ArrayTypeClass,
    "MAP": MapTypeClass,
    "STRUCT":RecordTypeClass,
}


@tenacity.retry(
    wait=tenacity.wait_exponential(),
    stop=tenacity.stop_after_attempt(5),
    reraise=True,
)
def push_to_kafka(metadata):
    kafka_broker = get_secret(DATAHUB_KAFKA_CONFIG_VAULT_PATH)
    schema_registry = get_secret(DATAHUB_KAFKA_SCHEMA_REGISTRY_VAULT_PATH)
    kafka_endpoint = f"{kafka_broker['host']}:{kafka_broker['port']}"
    schema_registry_endpoint = f"http://{schema_registry['host']}:{schema_registry['port']}"

    kafka_config = {
        "connection": {
            "bootstrap": kafka_endpoint,
            "schema_registry_url": schema_registry_endpoint,
            "schema_registry_config": {},
            "producer_config": {},
        }
    }
    emitter = DatahubKafkaEmitter(KafkaEmitterConfig.model_validate(kafka_config))
    for data in metadata:
        for packet in data:
            emitter.emit(
                packet,
                callback=lambda exc, message: None
                if message
                else print("failed to send to datahub :", exc),
            )
    emitter.flush()

def get_data_type(data_type: str):
    #handling cases with brackets present in datatype e.g Time()
    split_index = len(data_type)
    for i, x in enumerate(data_type):
        if x in ('(','<'):
            split_index = i
    data_type_identifier = data_type[0:split_index].upper()
    data_type_present = data_type_identifier in TRINO_DATAHUB_MAPPING
    return TRINO_DATAHUB_MAPPING[data_type_identifier]() if data_type_present else StringTypeClass()


class Dataset:
    """
    - schema_name : schema name of the dataset
    - table_name : dataset name
    - column_dtypes : array of dict where each dict contains name, data type and description of a column
    - table_description : small description of the dataset
    - data_platform : platform to which dataset was ingested
    - docs : array of dict where each dict contains url & description of the documents
    - properties : properties of the dataset
    - owner : owner of the dataset
    - table_creation_timestamp : mili epoch of table creation time
    - table_updation_timestamp : mili epoch of table updation time
    """

    def __init__(
        self,
        schema_name,
        table_name,
        column_dtypes,
        table_description,
        data_platform,
        docs,
        properties,
        owner,
        table_creation_timestamp,
        table_updation_timestamp,
        primary_keys=None,
        partition_keys=None,
    ):
        self.schema_name = schema_name
        self.table_name = table_name
        self.column_dtypes = column_dtypes
        self.table_description = table_description
        self.data_platform = data_platform
        self.docs = docs
        self.properties = properties
        self.owner = owner or DAG_OWNER
        self.table_creation_timestamp = table_creation_timestamp
        self.table_updation_timestamp = table_updation_timestamp
        self.current_timestamp = get_timestamp(datetime.now(pytz.timezone("Asia/Kolkata")))
        self.primary_keys = primary_keys
        self.partition_keys = partition_keys
        self.dataset_identifier = make_dataset_urn(
            platform=self.data_platform, name=f"{self.schema_name}.{self.table_name}", env="PROD"
        )

    def process_business_glossary(self):
        """
        Process links to documents containing the business logic of the dataset or any other useful info
        """
        elements = []
        if not self.docs:
            return None
        for doc in self.docs:
            elements.append(
                InstitutionalMemoryMetadataClass(
                    url=doc["url"],
                    description=doc["description"],
                    createStamp=AuditStampClass(
                        time=self.current_timestamp, actor=f"urn:li:corpuser:{self.owner}"
                    ),
                )
            )
        aspect: InstitutionalMemoryClass = InstitutionalMemoryClass(elements=elements)
        event: MetadataChangeProposalWrapper = MetadataChangeProposalWrapper(
            entityUrn=self.dataset_identifier, aspect=aspect
        )
        return event

    def process_columns(self):
        """
        Process columns of the dataset for Table & JSON view respectively
        """
        fields = []
        document_schema_fields = []
        for column in self.column_dtypes:
            fields.append(
                SchemaFieldClass(
                    fieldPath=column["name"],
                    nativeDataType=column["type"],
                    type=SchemaFieldDataTypeClass(type=get_data_type(data_type=column["type"])),
                    description=column["description"],
                    isPartitioningKey=True if column["name"] in self.partition_keys else False,
                )
            )
            document_schema_fields.append({"name": column["name"], "type": [column["type"]]})

        document_schema = {
            "type": "record",
            "name": f"{self.schema_name}.{self.table_name}",
            "namespace": "com.linkedin.dataset",
            "doc": self.table_description,
            "fields": document_schema_fields,
        }
        return fields, document_schema

    def process_custom_properties(self):
        meta = get_task_metadata()
        properties = {
            "Last updated from": meta["source"],
            "Last updated by": meta["user"]
        }
        if meta.get("dag_link"):
            properties["DAG Link"] = meta["dag_link"]
        if self.properties:
            properties.update(self.properties)
        aspect: DatasetPropertiesClass = DatasetPropertiesClass(
            customProperties=properties, description=self.table_description
        )
        event: MetadataChangeProposalWrapper = MetadataChangeProposalWrapper(
            entityUrn=self.dataset_identifier, aspect=aspect
        )
        return event

    def process_owner_metadata(self):
        if not self.owner:
            return None
        owner_name = self.owner
        owner_class_to_add = OwnerClass(
            owner=make_user_urn(owner_name), type=OwnershipTypeClass.TECHNICAL_OWNER
        )
        ownership_to_add = OwnershipClass(
            owners=[owner_class_to_add],
            lastModified=AuditStampClass(
                time=self.current_timestamp, actor=make_user_urn(owner_name)
            ),
        )

        event: MetadataChangeProposalWrapper = MetadataChangeProposalWrapper(
            entityUrn=self.dataset_identifier, aspect=ownership_to_add
        )
        return event

    def add_default_domain(self):
        domain_urn = make_domain_urn("blinkit")

        event: MetadataChangeProposalWrapper = MetadataChangeProposalWrapper(
            entityUrn=self.dataset_identifier,
            aspect=DomainsClass(domains=[domain_urn]),
        )
        return event

    def process_metadata(self):
        """
        Process metadata including owner info, business glossary and schema info
        """
        allowed_datahub_platforms = get_secret(PENCILBOX_CONFIG_VAULT_PATH)[
            "allowed_datahub_platforms"
        ].split(",")
        if self.data_platform not in allowed_datahub_platforms:
            raise ValueError(
                f"Data platform provided is not allowed. Please make sure data platform is from allowed data platforms Allowed Platforms: {allowed_datahub_platforms}"
            )
        datahub_events = []
        fields, document_schema = self.process_columns()
        schema_event: MetadataChangeProposalWrapper = MetadataChangeProposalWrapper(
            entityUrn=self.dataset_identifier,
            aspect=SchemaMetadataClass(
                schemaName=f"{self.schema_name}.{self.table_name}",
                platform=make_data_platform_urn(self.data_platform),
                version=0,
                hash="",
                platformSchema=OtherSchemaClass(rawSchema=json.dumps(document_schema)),
                created=AuditStampClass(
                    time=self.table_creation_timestamp or self.current_timestamp,
                    actor=f"urn:li:corpuser:{self.owner}",
                ),
                lastModified=AuditStampClass(
                    time=self.table_updation_timestamp or self.current_timestamp,
                    actor=f"urn:li:corpuser:{self.owner}",
                ),
                fields=fields,
                primaryKeys=self.primary_keys,
            ),
        )
        datahub_events.append(schema_event)
        if self.process_owner_metadata():
            datahub_events.append(self.process_owner_metadata())
        if self.process_business_glossary():
            datahub_events.append(self.process_business_glossary())
        datahub_events.append(self.process_custom_properties())
        datahub_events.append(self.add_default_domain())
        return datahub_events


def to_datahub_multi(entities):
    """
    Load metadata for multiple entities to datahub

    :param entities: entities for which metadata needs to be ingested
    :type entities: list(class of entities)
    """
    if not IS_PROD_ENV and not IS_AIRFLOW_ARS_ENV:
        print("Ingestion from staging is not allowed")
        return
    metadata = [entity.process_metadata() for entity in entities]
    try:
        push_to_kafka(metadata)
    except Exception as e:
        msg = (
            f"""Metadata load via `to_datahub` function failed due to following reason: ```{e}```"""
        )
        msg += f"\nTask metadata: ```{get_task_metadata()}```"
        send_slack_message(channel=P2_ALERT_SLACK_CHANNEL, text=msg)
        print(f"Error : {e}")


def to_datahub(
    schema_name,
    table_name,
    column_dtypes,
    table_description,
    data_platform,
    docs=None,
    properties=None,
    owner=None,
    table_creation_timestamp=None,
    table_updation_timestamp=None,
    primary_keys=[],
    partition_keys=[],
):
    to_datahub_multi(
        [
            Dataset(
                schema_name=schema_name.lower(),
                table_name=table_name.lower(),
                column_dtypes=column_dtypes,
                table_description=table_description,
                data_platform=data_platform.lower(),
                docs=docs,
                properties=properties,
                owner=owner,
                table_creation_timestamp=table_creation_timestamp,
                table_updation_timestamp=table_updation_timestamp,
                primary_keys=primary_keys,
                partition_keys=partition_keys,
            )
        ]
    )


def fetch_dataset(dataset_urn: str, aspects: List[str] = None):
    if aspects and set(aspects) - set(DatasetKeyClass.ASPECT_INFO["entityAspects"]):
        unsupported_aspects = ",".join(set(aspects) - set(DatasetKeyClass.ASPECT_INFO["entityAspects"]))
        raise AttributeError(f"Invalid aspect(s): {unsupported_aspects} while fetching datasets from Datahub")

    api_config = get_secret(DATAHUB_API_VAULT_PATH)
    api_url = f"http://{api_config['host']}:{api_config['port']}/openapi/v2/entity/dataset/{parse.quote(dataset_urn)}"
    headers = {
        "Authorization": f"Bearer {api_config['token']}"
    }
    params = {
        "systemMetadata": "false"
    }
    if aspects:
        params["aspects"] = aspects

    resp = requests.get(api_url, params=params, headers=headers)
    if resp.status_code != 200:
        return None
    return resp.json()


def fetch_dataset_custom_properties(schema_name: str, table_name: str, data_platform: str = "trino"):
    urn = make_dataset_urn(platform=data_platform, name=f"{schema_name}.{table_name}", env="PROD")
    try:
        datahub_response = fetch_dataset(
            dataset_urn=urn,
            aspects=[DatasetPropertiesClass.ASPECT_NAME]
        )
        return datahub_response[DatasetPropertiesClass.ASPECT_NAME]['value']["customProperties"]
    except Exception as e:
        warnings.warn(
            message=f"fetch_dataset_properties failed with Error: {e}",
            category=RuntimeWarning,
            stacklevel=2,
        )
        return None
