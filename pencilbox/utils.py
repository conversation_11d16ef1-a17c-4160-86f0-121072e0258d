# -*- coding: utf-8 -*-
import os
import random
import string
from enum import Enum
from decore.vault import get_secret

from .constants import (
    JHUB_EKS_VAULT_DEFAULT_ROLE,
    AIRFLOW_EKS_VAULT_DEFAULT_ROLE,
    PENCILBOX_CONFIG_VAULT_PATH,
)

IS_PROD_ENV = any(
    x in os.environ for x in ["AIRFLOW_DE_WEBSERVER_SERVICE_HOST", "AIRFLOW_WEBSERVER_SERVICE_HOST"]
)

IS_STAGING_ENV = any(
    x in os.environ for x in ["AIRFLOW_PREP_WEBSERVER_SERVICE_HOST", "JUPYTERHUB_USER"]
)

IS_PREP_ENV = "AIRFLOW_PREP_WEBSERVER_SERVICE_HOST" in os.environ

IS_JHUB = "JUPYTERHUB_USER" in os.environ

IS_AIRFLOW_DE_ENV = "AIRFLOW_DE_WEBSERVER_SERVICE_HOST" in os.environ
IS_AIRFLOW_COMMON_ENV = "AIRFLOW_WEBSERVER_SERVICE_HOST" in os.environ
IS_AIRFLOW_ARS_ENV = "AIRFLOW_ARS_WEBSERVER_SERVICE_HOST" in os.environ

# TODO: Remove DEFAULT_K8S_AUTH_ROLE and move this capability to dagger and set env while building DAGs
DEFAULT_K8S_AUTH_ROLE = JHUB_EKS_VAULT_DEFAULT_ROLE if IS_JHUB else AIRFLOW_EKS_VAULT_DEFAULT_ROLE


def random_string():
    letters = string.ascii_lowercase
    return "".join(random.choice(letters) for _ in range(7))


def get_timestamp(d=None):
    """Returns current timestamp in 13 digit epoch format"""
    return int(d.timestamp() * 1000)


def get_task_metadata():
    if "JUPYTERHUB_USER" in os.environ:
        metadata = {
            "user": os.environ.get("JUPYTERHUB_USER", "Unknown"),
            "task_id": "adhoc",
            "source": "Pencilbox|JHub",
        }
    elif "AIRFLOW_DAG_OWNER" in os.environ:
        metadata = {
            "user": os.environ.get("AIRFLOW_DAG_OWNER", "Unknown"),
            "task_id": os.environ.get("AIRFLOW_DAG_ID", "adhoc"),
            "run_id": os.environ.get("AIRFLOW_DAG_RUN_ID", "Unknown"),
            "source": "Pencilbox|Airflow",
            "dag_link": f"{os.environ.get('AIRFLOW_ENDPOINT', '<path not found>')}/tree?dag_id={os.environ.get('AIRFLOW_DAG_ID')}",
        }
    else:
        metadata = {
            "user": os.environ.get("USER", "Unknown"),
            "task_id": "adhoc",
            "source": f"Pencilbox|{os.environ.get('HOSTNAME', 'Unknown')}",
        }

    return metadata


def extract_company(email):
    return str(email.split("@")[-1].split(".")[0])


def extract_iceberg_metadata(etl_metric_data=None, dataset_custom_properties=None):
    iceberg_meta = dict()
    # First try and set the metadata using data fetched from datahub
    if dataset_custom_properties:
        if dataset_custom_properties.get(TrinoIcebergStatFields.LAST_COMPACTION_TIME.value):
            iceberg_meta[
                TrinoIcebergStatFields.LAST_COMPACTION_TIME.value
            ] = dataset_custom_properties.get(TrinoIcebergStatFields.LAST_COMPACTION_TIME.value)
        if dataset_custom_properties.get(TrinoIcebergStatFields.LAST_EXPIRE_SNAPSHOTS_TIME.value):
            iceberg_meta[
                TrinoIcebergStatFields.LAST_EXPIRE_SNAPSHOTS_TIME.value
            ] = dataset_custom_properties.get(
                TrinoIcebergStatFields.LAST_EXPIRE_SNAPSHOTS_TIME.value
            )
        if dataset_custom_properties.get(TrinoIcebergStatFields.LAST_DATA_UPDATE_TIME.value):
            iceberg_meta[
                TrinoIcebergStatFields.LAST_DATA_UPDATE_TIME.value
            ] = dataset_custom_properties.get(TrinoIcebergStatFields.LAST_DATA_UPDATE_TIME.value)

    # Try and set the metadata using observability data returned from decore.to_trino
    if etl_metric_data:
        if etl_metric_data.get(TrinoIcebergStatFields.COMPACTION_END_TIME.value):
            iceberg_meta[TrinoIcebergStatFields.LAST_COMPACTION_TIME.value] = etl_metric_data.get(
                TrinoIcebergStatFields.COMPACTION_END_TIME.value
            )
        if etl_metric_data.get(TrinoIcebergStatFields.EXPIRE_SNAPSHOTS_END_TIME.value):
            iceberg_meta[
                TrinoIcebergStatFields.LAST_EXPIRE_SNAPSHOTS_TIME.value
            ] = etl_metric_data.get(TrinoIcebergStatFields.EXPIRE_SNAPSHOTS_END_TIME.value)
        if etl_metric_data.get(TrinoIcebergStatFields.ICEBERG_WRITE_END_TIME.value):
            iceberg_meta[TrinoIcebergStatFields.LAST_DATA_UPDATE_TIME.value] = etl_metric_data.get(
                TrinoIcebergStatFields.ICEBERG_WRITE_END_TIME.value
            )
    return iceberg_meta

def get_trino_source(task_id: str) -> str:
    
    if IS_AIRFLOW_COMMON_ENV:
        formatted_task_id = task_id.rsplit("_v", 1)[0] 
        source_taskid_mappings = get_secret(PENCILBOX_CONFIG_VAULT_PATH)[
                "source_taskid_mappings"
            ]
        # Iterate over each source and its list of dag_ids
        for source, dag_list in source_taskid_mappings.items():
            if formatted_task_id in dag_list:
                return source
        # Optional fallback if no match found
        return ""

    elif IS_AIRFLOW_DE_ENV:
        return "airflow-de"
    elif IS_AIRFLOW_ARS_ENV:
        return "airflow-ars"
    return ""

class ToKafkaObservabilityFields(Enum):
    TASK_METADATA = "task_metadata"
    TOPIC_NAME = "topic_name"
    KEY_PASSED = "key_passed"
    ENVIRONMENT = "environment"
    MESSAGE_COUNT = "message_count"
    SAMPLE_MESSAGE = "sample_message"
    START_TIMESTAMP = "start_timestamp"
    END_TIMESTAMP = "end_timestamp"
    ERROR = "error"


class TrinoIcebergStatFields(Enum):
    COMPACTION_END_TIME = "compaction_end_time"
    EXPIRE_SNAPSHOTS_END_TIME = "expire_snapshots_end_time"
    ICEBERG_WRITE_END_TIME = "iceberg_write_end_time"
    LAST_COMPACTION_TIME = "Last Compaction Timestamp(UTC)"
    LAST_EXPIRE_SNAPSHOTS_TIME = "Last Expire Snapshots Timestamp(UTC)"
    LAST_DATA_UPDATE_TIME = "Last Data Updated Timestamp(UTC)"