import os
import warnings
from slack_sdk.web.base_client import SlackResponse
from slack_sdk.errors import SlackApiError

from .connections import get_slack_client
from .utils import get_task_metadata
from .constants import SLACK_BOT_NAME


def _response_handler(responses):
    errors = []
    results = []
    for response in responses:
        if not isinstance(response, SlackResponse):
            response = response.json()
        if not response["ok"]:
            errors.append(response)
        else:
            results.append(response)
    if errors:
        raise Exception(errors)
    else:
        return results


def send_slack_message(channel, text=None, user=None, files=[], thread_ts=None, source_info=True):
    def _send_message(channel, text, thread_ts):
        responses = []
        client = get_slack_client(SLACK_BOT_NAME)
        channel_id = channel

        # Retrieving metadata
        metadata = get_task_metadata()
        user = metadata["user"]
        source = metadata["source"].split("|")[-1] + "|" + user
        source_text = (
            f"source: <mailto:{user}|{source}>"
            if metadata["task_id"] == "adhoc"
            else f"source: <{metadata['dag_link']}|{source}>"
        )

        # Combine text and source info message in a single call
        combined_text = (
            text
            if not source_info
            else (text + "\n\n" + f"_{source_text}_" if text else f"_{source_text}_")
        )

        if combined_text:
            message_kwargs = {"channel": channel, "text": combined_text}
            if thread_ts:
                message_kwargs["thread_ts"] = thread_ts
            response = client.chat_postMessage(**message_kwargs)
            responses.append(response)
            channel_id = response["channel"]

        file_uploads = []
        for filepath in files:
            if not os.path.exists(filepath):
                raise FileNotFoundError(f"File not found at {filepath}")
            file_uploads.append({"filename": os.path.basename(filepath), "file": filepath})

        if file_uploads:
            try:
                responses.append(
                    client.files_upload_v2(
                        file_uploads=file_uploads, channel=channel_id, thread_ts=thread_ts
                    )
                )
            except SlackApiError as e:
                error_message = str(e)
                if "Server Error" in error_message:
                    warnings.warn(
                        "Failed to upload document due to internal server error",
                        category=RuntimeWarning,
                        stacklevel=2,
                    )
                else:
                    raise e

        return responses

    if not text and not files:
        raise Exception("Please pass either 'text' or 'files' in the function 'send_slack_message'")

    if user:
        warnings.warn(
            "Parameter 'user' in function 'send_slack_message' is Deprecated. Please avoid passing 'user' in function 'send_slack_message'",
            category=DeprecationWarning,
            stacklevel=2,
        )

    responses = _send_message(channel, text, thread_ts)
    return _response_handler(responses)
