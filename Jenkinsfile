pipeline {
  agent any
  options {
    skipStagesAfterUnstable()
    buildDiscarder(logRotator(numToKeepStr: '15', daysToKeepStr: '15'))
    disableConcurrentBuilds()
  }
  environment {
    VAULT_TOKEN = credentials('cigro<PERSON>-jenkins')
    VAULT_URL = "https://vault-ui-prod.grofer.io/"
    SLACK_CHANNEL = "#bl-data-deployments"
    PROJECT = env.JOB_NAME.toLowerCase()
  }
  stages {
    stage("Run Test") {
      when { changeRequest() }
      steps {
        script {
          sh(script: 'echo "Running tests..."', returnStdout: false)
          sh(script: 'make dockerized-tests', returnStdout: false)
        }
      }
    }

    stage("Release") {
      when { tag "v*" }
      steps {
        script {
          sh(script: 'echo "Releasing tag"', returnStdout: false)
          sh(script: 'make docker-release', returnStdout: false)
        }
      }
    }
  }

  post {
    success {
      script {
        if (env.BR<PERSON>CH_NAME == "master") {
          slackSend(color: "good", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} completed successfully. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
        }
      }
    }

    failure {
      script {
        if (env.BRANCH_NAME == "master") {
          slackSend(color: "danger", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} failed. Please contact data on call. See ${env.RUN_DISPLAY_URL} for details.", channel: "#bl-data-alerts-p0")
        }
      }
    }
  }
}
