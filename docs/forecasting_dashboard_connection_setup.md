# Forecasting Dashboard RDS Connection Setup

This document describes how to set up the Forecasting Dashboard RDS connection in Pencil Box.

## Overview

The Forecasting Dashboard connection provides access to the `forecasting_dashboard` RDS PostgreSQL database with both read-only and read-write endpoints.

## Connection Details

- **Connection Name**: `[Database] Forecasting Dashboard`
- **Database Type**: PostgreSQL (RDS)
- **Database Name**: `forecasting_dashboard`
- **Read-Only Endpoint**: `prod-forecasting-dashboard-cluster.cluster-ro-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com`
- **Read-Write Endpoint**: `prod-forecasting-dashboard-cluster.cluster-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com`

## Vault Configuration

### 1. Connection Map Entry

Add the following entry to the connection maps in Vault:

**Production Connection Map** (`dse/pencilbox/key_map`):
```json
{
  "[Database] Forecasting Dashboard": "data/rds/forecasting-dashboard-prod"
}
```

**JupyterHub Connection Map** (`dse/pencilbox/key_map_jhub`):
```json
{
  "[Database] Forecasting Dashboard": "data/rds/forecasting-dashboard-prod"
}
```

### 2. Connection Details

Create the connection details at the vault path `data/rds/forecasting-dashboard-prod`:

```json
{
  "type": "postgres",
  "host": "prod-forecasting-dashboard-cluster.cluster-ro-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com",
  "port": 5432,
  "user": "<username>",
  "password": "<password>",
  "dbname": "forecasting_dashboard",
  "uri": "postgresql://<username>:<password>@prod-forecasting-dashboard-cluster.cluster-ro-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com:5432/forecasting_dashboard"
}
```

**Note**: Replace `<username>` and `<password>` with the actual database credentials.

### 3. Read-Write Connection (Optional)

If you need a separate read-write connection, create an additional entry:

**Connection Map Entry**:
```json
{
  "[Database] Forecasting Dashboard RW": "data/rds/forecasting-dashboard-prod-rw"
}
```

**Connection Details** at `data/rds/forecasting-dashboard-prod-rw`:
```json
{
  "type": "postgres",
  "host": "prod-forecasting-dashboard-cluster.cluster-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com",
  "port": 5432,
  "user": "<username>",
  "password": "<password>",
  "dbname": "forecasting_dashboard",
  "uri": "postgresql://<username>:<password>@prod-forecasting-dashboard-cluster.cluster-cmn74bewi8rx.ap-southeast-1.rds.amazonaws.com:5432/forecasting_dashboard"
}
```

## Usage Examples

### Basic Connection

```python
import pencilbox as pb
import pandas as pd

# Get connection
conn = pb.get_connection("[Database] Forecasting Dashboard")

# Run a query
sql = "SELECT * FROM your_table LIMIT 10"
df = pd.read_sql_query(sql, conn)
```

### Using with Context Manager

```python
import pencilbox as pb
import pandas as pd

# Get connection
with pb.get_connection("[Database] Forecasting Dashboard") as conn:
    sql = "SELECT COUNT(*) as total_records FROM your_table"
    df = pd.read_sql_query(sql, conn)
    print(f"Total records: {df['total_records'].iloc[0]}")
```

## Security Considerations

1. **SSL Connection**: The connection automatically uses `sslmode=prefer` for secure connections
2. **Read-Only Access**: Use the read-only endpoint for analytical queries to avoid impacting production workloads
3. **Credentials**: Ensure database credentials are properly secured in Vault with appropriate access controls

## Troubleshooting

### Connection Issues

1. **SSL Errors**: The connection uses `sslmode=prefer` by default. If you encounter SSL issues, verify the RDS instance SSL configuration.

2. **Authentication Errors**: Verify that:
   - The username and password in Vault are correct
   - The database user has appropriate permissions
   - The security groups allow connections from your environment

3. **Network Issues**: Ensure that:
   - The RDS instance is accessible from your network
   - Security groups and NACLs allow the connection
   - DNS resolution is working for the RDS endpoint

### Testing the Connection

```python
import pencilbox as pb

try:
    conn = pb.get_connection("[Database] Forecasting Dashboard")
    result = conn.execute("SELECT 1 as test")
    print("Connection successful:", result.fetchone())
except Exception as e:
    print("Connection failed:", str(e))
```

## Maintenance

- **Credential Rotation**: Update the Vault secrets when database credentials are rotated
- **Endpoint Changes**: Update the host and URI in Vault if RDS endpoints change
- **Access Reviews**: Regularly review who has access to the connection in Vault
