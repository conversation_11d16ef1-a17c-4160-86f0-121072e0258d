# Changelog

## [Unreleased](https://github.com/grofers/pencilbox/tree/HEAD)

[Full Changelog](https://github.com/grofers/pencilbox/compare/v0.2.2...HEAD)

**Closed issues:**

- decore is missing in requirements.txt [\#79](https://github.com/grofers/pencilbox/issues/79)

**Merged pull requests:**

- DOC-1597: Fixed Dag Link [\#125](https://github.com/grofers/pencilbox/pull/125) ([Sunil0612](https://github.com/Sunil0612))
- DP-1210 : Fix simultaneous ingestion logic [\#123](https://github.com/grofers/pencilbox/pull/123) ([shubhamg931](https://github.com/shubhamg931))
- DP-1210 : Ingest catalog to v0.6 and v0.8.14 datahub simultaneously [\#122](https://github.com/grofers/pencilbox/pull/122) ([shubhamg931](https://github.com/shubhamg931))
- DP-1068 : Add to\_datahub\_multi\(\) and Dataset class to \_\_init\_\_.py [\#121](https://github.com/grofers/pencilbox/pull/121) ([shubhamg931](https://github.com/shubhamg931))
- DP-1071 : Add pre-commit support [\#119](https://github.com/grofers/pencilbox/pull/119) ([shubhamg931](https://github.com/shubhamg931))
- DP-1068, DOC-1424, DOC-1520 : Allow properties updation via to\_datahub [\#118](https://github.com/grofers/pencilbox/pull/118) ([shubhamg931](https://github.com/shubhamg931))
- DOC-1534 Fix README examples [\#117](https://github.com/grofers/pencilbox/pull/117) ([Sangarshanan](https://github.com/Sangarshanan))
- DP-979 Support queries on presto  [\#116](https://github.com/grofers/pencilbox/pull/116) ([Sangarshanan](https://github.com/Sangarshanan))
- DOC-1385 : Fix query annotation bug [\#115](https://github.com/grofers/pencilbox/pull/115) ([shubhamg931](https://github.com/shubhamg931))
- DOC-1379 Add doc for formatting slack messages [\#114](https://github.com/grofers/pencilbox/pull/114) ([Sangarshanan](https://github.com/Sangarshanan))
- DOC-1377 Pin requirements [\#113](https://github.com/grofers/pencilbox/pull/113) ([Sangarshanan](https://github.com/Sangarshanan))
- DOC-1361 Fix pencilbox tests [\#112](https://github.com/grofers/pencilbox/pull/112) ([Sangarshanan](https://github.com/Sangarshanan))
- DP-779 : Fetching DAG\_OWNER from airflow and annotating description [\#111](https://github.com/grofers/pencilbox/pull/111) ([Sunil0612](https://github.com/Sunil0612))
- DOC-1314 : make catalog compulsory for all environments  [\#110](https://github.com/grofers/pencilbox/pull/110) ([Sunil0612](https://github.com/Sunil0612))
- hotfix Add Missed Comma [\#109](https://github.com/grofers/pencilbox/pull/109) ([Sangarshanan](https://github.com/Sangarshanan))
- DOC-1269 Fix slack tests [\#107](https://github.com/grofers/pencilbox/pull/107) ([Sangarshanan](https://github.com/Sangarshanan))
- DOC-1268 : Datahub test failure due to hardcoded DAG\_OWNER [\#106](https://github.com/grofers/pencilbox/pull/106) ([shubhamg931](https://github.com/shubhamg931))
- DOC-1249 : deprecation warning added [\#105](https://github.com/grofers/pencilbox/pull/105) ([Sunil0612](https://github.com/Sunil0612))
- DOC-1246 : Exception raised for invalid schema and table name [\#102](https://github.com/grofers/pencilbox/pull/102) ([Sunil0612](https://github.com/Sunil0612))
- ODIC-442 : Add documentation link to error msg [\#101](https://github.com/grofers/pencilbox/pull/101) ([shubhamg931](https://github.com/shubhamg931))
- ODIC-442 : Bump version: 0.2.1 → 0.2.2 [\#100](https://github.com/grofers/pencilbox/pull/100) ([satyamkrishna](https://github.com/satyamkrishna))
- ODIC-442 : Catalog updation mandatory on Jhub [\#99](https://github.com/grofers/pencilbox/pull/99) ([shubhamg931](https://github.com/shubhamg931))
- DOC-1213 : Release 0.2.1 [\#98](https://github.com/grofers/pencilbox/pull/98) ([shubhamg931](https://github.com/shubhamg931))
- ODIC-406 : Correction in to\_datahub function documentation [\#97](https://github.com/grofers/pencilbox/pull/97) ([shubhamg931](https://github.com/shubhamg931))
- ODIC-406 : Document to\_redshift and to\_datahub function [\#96](https://github.com/grofers/pencilbox/pull/96) ([shubhamg931](https://github.com/shubhamg931))
- ODIC-406 : Changing default fabric type to PROD in to\_datahub function [\#94](https://github.com/grofers/pencilbox/pull/94) ([shubhamg931](https://github.com/shubhamg931))

## [v0.2.2](https://github.com/grofers/pencilbox/tree/v0.2.2) (2021-02-09)

[Full Changelog](https://github.com/grofers/pencilbox/compare/v0.2.1...v0.2.2)

## [v0.2.1](https://github.com/grofers/pencilbox/tree/v0.2.1) (2021-01-28)

[Full Changelog](https://github.com/grofers/pencilbox/compare/v0.2.0...v0.2.1)

## [v0.2.0](https://github.com/grofers/pencilbox/tree/v0.2.0) (2021-01-04)

[Full Changelog](https://github.com/grofers/pencilbox/compare/v0.1.3...v0.2.0)

**Fixed bugs:**

- \[MRG\] Reusing Google Authentication [\#44](https://github.com/grofers/pencilbox/pull/44) ([Sangarshanan](https://github.com/Sangarshanan))

**Merged pull requests:**

- ODIC-406 : Adding send to datahub functionality in to\_redshift [\#93](https://github.com/grofers/pencilbox/pull/93) ([shubhamg931](https://github.com/shubhamg931))
- SDSE-211 Fix slack tests [\#92](https://github.com/grofers/pencilbox/pull/92) ([Sangarshanan](https://github.com/Sangarshanan))
- SDSE-211 Add a working to\_redshift example on Readme [\#91](https://github.com/grofers/pencilbox/pull/91) ([Sangarshanan](https://github.com/Sangarshanan))
- SDSE-184 Update pypi registry [\#90](https://github.com/grofers/pencilbox/pull/90) ([Sangarshanan](https://github.com/Sangarshanan))
- SDSE-182 Replace sleep with polling [\#87](https://github.com/grofers/pencilbox/pull/87) ([Sangarshanan](https://github.com/Sangarshanan))
- \[SDSE-184\]  Automate parts of release process [\#86](https://github.com/grofers/pencilbox/pull/86) ([plant99](https://github.com/plant99))
- \[SDSE-176\] Use token authentication on vault [\#78](https://github.com/grofers/pencilbox/pull/78) ([deepu-tp](https://github.com/deepu-tp))
- \[SDSE-95\] add example to tag users in slack messages [\#75](https://github.com/grofers/pencilbox/pull/75) ([Sudeepam97](https://github.com/Sudeepam97))
- \[SDSE-166\] Dockerize tests  [\#74](https://github.com/grofers/pencilbox/pull/74) ([Sudeepam97](https://github.com/Sudeepam97))
- SDSE-80 Add slack client to send messages [\#69](https://github.com/grofers/pencilbox/pull/69) ([Sangarshanan](https://github.com/Sangarshanan))
- \[DR-1067\] Fix PII access via jhub [\#66](https://github.com/grofers/pencilbox/pull/66) ([deepu-tp](https://github.com/deepu-tp))
- \[MRG\] Adding Decore link in README [\#65](https://github.com/grofers/pencilbox/pull/65) ([shubhamg931](https://github.com/shubhamg931))
- Monkey patch conn.execute\(\) to facilitate comments for incoming queries [\#64](https://github.com/grofers/pencilbox/pull/64) ([Sudeepam97](https://github.com/Sudeepam97))
- Remove eval from exception handling [\#63](https://github.com/grofers/pencilbox/pull/63) ([Sangarshanan](https://github.com/Sangarshanan))
- \[README\]\[HOTFIX\] Update readme with service\_account param [\#61](https://github.com/grofers/pencilbox/pull/61) ([Sangarshanan](https://github.com/Sangarshanan))
- update readme and add FAQ for sheets  [\#58](https://github.com/grofers/pencilbox/pull/58) ([Sangarshanan](https://github.com/Sangarshanan))
- \[MRG\] Add new service account and invalidate cache [\#55](https://github.com/grofers/pencilbox/pull/55) ([Sangarshanan](https://github.com/Sangarshanan))
- \[MRG\] Add dataframe to google sheets  [\#54](https://github.com/grofers/pencilbox/pull/54) ([Sangarshanan](https://github.com/Sangarshanan))
- \[MRG\] Add decore [\#48](https://github.com/grofers/pencilbox/pull/48) ([vinayak-mehta](https://github.com/vinayak-mehta))
- \[MRG\] Use connection type instead of id [\#38](https://github.com/grofers/pencilbox/pull/38) ([vinayak-mehta](https://github.com/vinayak-mehta))
- add pre\_pool\_ping argument to create\_engine [\#34](https://github.com/grofers/pencilbox/pull/34) ([Sangarshanan](https://github.com/Sangarshanan))
- Fix informative STL load exception [\#30](https://github.com/grofers/pencilbox/pull/30) ([Sangarshanan](https://github.com/Sangarshanan))
- Grant permissions after table creation [\#29](https://github.com/grofers/pencilbox/pull/29) ([Sangarshanan](https://github.com/Sangarshanan))
- \[MRG\] Show stl load error on exception [\#28](https://github.com/grofers/pencilbox/pull/28) ([Sangarshanan](https://github.com/Sangarshanan))
- Fix bug-26 [\#27](https://github.com/grofers/pencilbox/pull/27) ([deepu-tp](https://github.com/deepu-tp))
- Adding function to download file from s3 [\#17](https://github.com/grofers/pencilbox/pull/17) ([niteshnicholas](https://github.com/niteshnicholas))
- Reusing hvac client and connections [\#13](https://github.com/grofers/pencilbox/pull/13) ([Sangarshanan](https://github.com/Sangarshanan))
- import changes in sheets.py [\#9](https://github.com/grofers/pencilbox/pull/9) ([Sangarshanan](https://github.com/Sangarshanan))
- \[MRG\] Change to psycopg2-binary for easier builds [\#8](https://github.com/grofers/pencilbox/pull/8) ([deepu-tp](https://github.com/deepu-tp))
- Add google sheet download function [\#6](https://github.com/grofers/pencilbox/pull/6) ([Sangarshanan](https://github.com/Sangarshanan))
- \[MRG\] Add method to get secrets. Remove unused attribute  in get connection [\#4](https://github.com/grofers/pencilbox/pull/4) ([deepu-tp](https://github.com/deepu-tp))
- \[MRG\] Add helper methods [\#3](https://github.com/grofers/pencilbox/pull/3) ([vinayak-mehta](https://github.com/vinayak-mehta))

## [v0.1.3](https://github.com/grofers/pencilbox/tree/v0.1.3) (2020-11-10)

[Full Changelog](https://github.com/grofers/pencilbox/compare/v0.1.2...v0.1.3)

## [v0.1.2](https://github.com/grofers/pencilbox/tree/v0.1.2) (2020-11-10)

[Full Changelog](https://github.com/grofers/pencilbox/compare/e0028e29f6d46766fe79febb96bfd2e4edd04522...v0.1.2)

**Implemented enhancements:**

- Add option to specify email template name in notebook [\#50](https://github.com/grofers/pencilbox/issues/50)
- Change sheet access service account name to something better  [\#32](https://github.com/grofers/pencilbox/issues/32)
- Raise exception for stl load errors  [\#24](https://github.com/grofers/pencilbox/issues/24)
- Add slack alerts [\#18](https://github.com/grofers/pencilbox/issues/18)
- Add from\_s3 method to let anyone access files on s3 [\#12](https://github.com/grofers/pencilbox/issues/12)
- Reuse hvac client and connections [\#11](https://github.com/grofers/pencilbox/issues/11)
- Set up Compass- Pencilbox geospatial support  [\#7](https://github.com/grofers/pencilbox/issues/7)
- Add function to download google sheet [\#5](https://github.com/grofers/pencilbox/issues/5)

**Fixed bugs:**

- Remove authentication to google at the time of pencilbox import. [\#41](https://github.com/grofers/pencilbox/issues/41)
- Raise exception when to\_redshift column dtype fields are empty [\#39](https://github.com/grofers/pencilbox/issues/39)
- \[Bug\] Email example is incorrect [\#26](https://github.com/grofers/pencilbox/issues/26)
- Run grant statements after creating new table on redshift [\#23](https://github.com/grofers/pencilbox/issues/23)

**Closed issues:**

- Clone pencilbox into Jhub shared folders [\#49](https://github.com/grofers/pencilbox/issues/49)
- Adding support for reusable ETL operations like insert, append, upsert [\#40](https://github.com/grofers/pencilbox/issues/40)
- Connection to mysql is getting lost in between the query run [\#33](https://github.com/grofers/pencilbox/issues/33)
- Include table name as prefix in temp file [\#25](https://github.com/grofers/pencilbox/issues/25)
- Pickup Environment Variables from Code. [\#22](https://github.com/grofers/pencilbox/issues/22)
- Test AWS credentials source for boto3 [\#16](https://github.com/grofers/pencilbox/issues/16)



\* *This Changelog was automatically generated by [github_changelog_generator](https://github.com/github-changelog-generator/github-changelog-generator)*
